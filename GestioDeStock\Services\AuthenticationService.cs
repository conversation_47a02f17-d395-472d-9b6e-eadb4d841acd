using BCrypt.Net;
using GestioDeStock.Data.Repositories;
using GestioDeStock.Models;
using System.Threading.Tasks;

namespace GestioDeStock.Services
{
    public class AuthenticationService
    {
        private readonly UserRepository _userRepository;
        private User? _currentUser;

        public AuthenticationService(UserRepository userRepository)
        {
            _userRepository = userRepository;
        }

        public User? CurrentUser => _currentUser;
        public bool IsAuthenticated => _currentUser != null;

        public async Task<bool> LoginAsync(string username, string password)
        {
            try
            {
                var user = await _userRepository.GetByUsernameAsync(username);
                if (user == null || !user.IsActive)
                    return false;

                if (BCrypt.Net.BCrypt.Verify(password, user.PasswordHash))
                {
                    _currentUser = user;
                    await _userRepository.UpdateLastLoginAsync(user.Id);
                    return true;
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        public void Logout()
        {
            _currentUser = null;
        }

        public string HashPassword(string password)
        {
            return BCrypt.Net.BCrypt.HashPassword(password);
        }

        public bool VerifyPassword(string password, string hash)
        {
            return BCrypt.Net.BCrypt.Verify(password, hash);
        }

        public bool HasPermission(UserRole requiredRole)
        {
            if (!IsAuthenticated)
                return false;

            // Administrateur a tous les droits
            if (_currentUser!.Role == UserRole.Administrateur)
                return true;

            // Vérifier les permissions selon le rôle
            return _currentUser.Role == requiredRole;
        }

        public bool CanAccessSales()
        {
            return IsAuthenticated && (_currentUser!.Role == UserRole.Administrateur || 
                                     _currentUser.Role == UserRole.Vendeur || 
                                     _currentUser.Role == UserRole.Caissier);
        }

        public bool CanAccessPurchases()
        {
            return IsAuthenticated && (_currentUser!.Role == UserRole.Administrateur || 
                                     _currentUser.Role == UserRole.Gestionnaire);
        }

        public bool CanAccessReports()
        {
            return IsAuthenticated && (_currentUser!.Role == UserRole.Administrateur || 
                                     _currentUser.Role == UserRole.Gestionnaire);
        }

        public bool CanAccessSettings()
        {
            return IsAuthenticated && _currentUser!.Role == UserRole.Administrateur;
        }

        public bool CanManageUsers()
        {
            return IsAuthenticated && _currentUser!.Role == UserRole.Administrateur;
        }

        public bool CanManageProducts()
        {
            return IsAuthenticated && (_currentUser!.Role == UserRole.Administrateur || 
                                     _currentUser.Role == UserRole.Gestionnaire);
        }
    }
}
