using System;

namespace GestioDeStock.Models
{
    public class ProductStock
    {
        public int Id { get; set; }
        public int ProductId { get; set; }
        public int WarehouseId { get; set; }
        public decimal QuantityAvailable { get; set; } = 0.000m;
        public decimal QuantityReserved { get; set; } = 0.000m;
        public decimal QuantityTotal => QuantityAvailable + QuantityReserved;
        public decimal LastCost { get; set; } = 0.00m;
        public decimal AverageCost { get; set; } = 0.00m;
        public DateTime LastUpdated { get; set; } = DateTime.Now;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // For Dapper mapping from joined queries
        public string? ProductCode { get; set; }
        public string? ProductName { get; set; }
        public string? WarehouseName { get; set; }
        public decimal MinStockAlert { get; set; }
        public decimal? MaxStockAlert { get; set; }

        // Navigation properties
        public Product Product { get; set; } = null!;
        public Warehouse Warehouse { get; set; } = null!;
    }
}
