{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=gestion_stock;Uid=root;Pwd=;CharSet=utf8mb4;Connection Timeout=30;"}, "AppSettings": {"CompanyName": "<PERSON>", "ApplicationVersion": "1.0.0", "DefaultLanguage": "fr-FR", "DefaultCurrency": "DA", "DefaultTVARate": 19.0, "BackupPath": "C:\\Backups\\GestionStock", "ReportsPath": "C:\\Reports\\GestionStock", "MaxLoginAttempts": 3, "SessionTimeoutMinutes": 60, "AutoBackupEnabled": true, "AutoBackupIntervalHours": 24}, "PrinterSettings": {"ThermalPrinterEnabled": false, "DefaultPrinterName": "", "PaperWidth": 80, "PrintLogo": true, "PrintFooter": true}, "BarcodeSettings": {"ScaleBarcodeEnabled": true, "WeightPrefix": "22", "PricePrefix": "02", "ProductCodeLength": 4, "WeightPrecision": 3, "PricePrecision": 2}, "SecuritySettings": {"PasswordMinLength": 6, "RequireUppercase": false, "RequireNumbers": false, "RequireSpecialChars": false, "PasswordExpiryDays": 0, "EnableAuditLog": true}, "UISettings": {"Theme": "Light", "PrimaryColor": "Blue", "SecondaryColor": "Orange", "FontSize": "Medium", "ShowAnimations": true, "AutoRefreshInterval": 30}, "BusinessRules": {"AllowNegativeStock": false, "RequireCustomerForSale": false, "AutoCalculatePMP": true, "DefaultStockMethod": "FIFO", "MinimumSaleAmount": 0.01, "MaximumDiscountPercent": 50.0}, "TimbreSettings": {"EnableTimbre": true, "TimbreRates": {"Level1": {"MinAmount": 0, "MaxAmount": 300, "Rate": 0}, "Level2": {"MinAmount": 300.01, "MaxAmount": 30000, "Rate": 1}, "Level3": {"MinAmount": 30000.01, "MaxAmount": 100000, "RatePerHundred": 1.5}}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "EnableFileLogging": true, "LogFilePath": "Logs\\app.log", "MaxLogFileSizeMB": 10, "MaxLogFiles": 5}}