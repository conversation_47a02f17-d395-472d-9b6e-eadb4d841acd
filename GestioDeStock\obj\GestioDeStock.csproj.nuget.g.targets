﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)system.text.encodings.web\9.0.7\buildTransitive\netcoreapp2.0\System.Text.Encodings.Web.targets" Condition="Exists('$(NuGetPackageRoot)system.text.encodings.web\9.0.7\buildTransitive\netcoreapp2.0\System.Text.Encodings.Web.targets')" />
    <Import Project="$(NuGetPackageRoot)system.io.pipelines\9.0.7\buildTransitive\netcoreapp2.0\System.IO.Pipelines.targets" Condition="Exists('$(NuGetPackageRoot)system.io.pipelines\9.0.7\buildTransitive\netcoreapp2.0\System.IO.Pipelines.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.bcl.asyncinterfaces\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Bcl.AsyncInterfaces.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.bcl.asyncinterfaces\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Bcl.AsyncInterfaces.targets')" />
    <Import Project="$(NuGetPackageRoot)system.text.json\9.0.7\buildTransitive\netcoreapp2.0\System.Text.Json.targets" Condition="Exists('$(NuGetPackageRoot)system.text.json\9.0.7\buildTransitive\netcoreapp2.0\System.Text.Json.targets')" />
    <Import Project="$(NuGetPackageRoot)system.security.cryptography.protecteddata\9.0.7\buildTransitive\netcoreapp2.0\System.Security.Cryptography.ProtectedData.targets" Condition="Exists('$(NuGetPackageRoot)system.security.cryptography.protecteddata\9.0.7\buildTransitive\netcoreapp2.0\System.Security.Cryptography.ProtectedData.targets')" />
    <Import Project="$(NuGetPackageRoot)system.drawing.common\9.0.7\buildTransitive\netcoreapp2.0\System.Drawing.Common.targets" Condition="Exists('$(NuGetPackageRoot)system.drawing.common\9.0.7\buildTransitive\netcoreapp2.0\System.Drawing.Common.targets')" />
    <Import Project="$(NuGetPackageRoot)system.diagnostics.diagnosticsource\9.0.7\buildTransitive\netcoreapp2.0\System.Diagnostics.DiagnosticSource.targets" Condition="Exists('$(NuGetPackageRoot)system.diagnostics.diagnosticsource\9.0.7\buildTransitive\netcoreapp2.0\System.Diagnostics.DiagnosticSource.targets')" />
    <Import Project="$(NuGetPackageRoot)system.configuration.configurationmanager\9.0.7\buildTransitive\netcoreapp2.0\System.Configuration.ConfigurationManager.targets" Condition="Exists('$(NuGetPackageRoot)system.configuration.configurationmanager\9.0.7\buildTransitive\netcoreapp2.0\System.Configuration.ConfigurationManager.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.primitives\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Primitives.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.primitives\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Primitives.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.dependencyinjection.abstractions\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.DependencyInjection.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.dependencyinjection.abstractions\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.DependencyInjection.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.options\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Options.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.options\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Options.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Logging.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.dependencyinjection\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.DependencyInjection.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.dependencyinjection\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.DependencyInjection.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Logging.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Logging.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.filesystemglobbing\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.FileSystemGlobbing.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.filesystemglobbing\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.FileSystemGlobbing.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.fileproviders.abstractions\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.FileProviders.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.fileproviders.abstractions\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.FileProviders.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.fileproviders.physical\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.FileProviders.Physical.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.fileproviders.physical\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.FileProviders.Physical.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.abstractions\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.abstractions\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.fileextensions\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.FileExtensions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.fileextensions\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.FileExtensions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.json\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.Json.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.json\9.0.7\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.Json.targets')" />
    <Import Project="$(NuGetPackageRoot)materialdesignthemes\5.2.1\build\MaterialDesignThemes.targets" Condition="Exists('$(NuGetPackageRoot)materialdesignthemes\5.2.1\build\MaterialDesignThemes.targets')" />
  </ImportGroup>
</Project>