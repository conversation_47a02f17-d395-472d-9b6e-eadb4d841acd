namespace GestioDeStock.Models
{
    public class CompanySettings : BaseEntity
    {
        public string CompanyName { get; set; } = string.Empty;
        public string? CompanyAddress { get; set; }
        public string? CompanyPhone { get; set; }
        public string? CompanyEmail { get; set; }
        public byte[]? CompanyLogo { get; set; }
        public string? Nif { get; set; }
        public string? Nis { get; set; }
        public string? Rc { get; set; }
        public string? Article { get; set; }
        public decimal TvaRate { get; set; } = 19.00m;
        public string Currency { get; set; } = "DA";
        public DateTime? FiscalYearStart { get; set; }
    }
}
