using GestioDeStock.Data;
using GestioDeStock.Data.Repositories;
using GestioDeStock.Models;
using GestioDeStock.Services;
using System.Windows;
using System.Windows.Controls;

namespace GestioDeStock.Views.Dialogs
{
    public partial class OpenCashDialog : Window
    {
        private readonly CashSessionRepository _sessionRepository;
        private readonly AuthenticationService _authService;

        public decimal OpeningAmount { get; private set; }
        public string Notes { get; private set; } = string.Empty;
        public string SessionNumber { get; private set; } = string.Empty;

        public OpenCashDialog(AuthenticationService authService)
        {
            InitializeComponent();
            _authService = authService;
            
            var config = ConfigurationService.Instance;
            var database = new DatabaseConnection(config.GetConnectionString());
            _sessionRepository = new CashSessionRepository(database);
            
            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                // Set user and warehouse info
                UserTextBox.Text = _authService.CurrentUser?.FullName ?? "Utilisateur";
                WarehouseTextBox.Text = "Magasin Principal"; // TODO: Get from settings
                
                // Generate session number
                SessionNumber = await _sessionRepository.GetNextSessionNumberAsync();
                SessionNumberTextBlock.Text = $"Session: {SessionNumber}";
                
                // Set current date/time
                DateTimeTextBlock.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm");
                
                // Focus on opening amount
                OpeningAmountTextBox.Focus();
                OpeningAmountTextBox.SelectAll();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement: {ex.Message}", 
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OpeningAmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (decimal.TryParse(OpeningAmountTextBox.Text, out decimal amount) && amount >= 0)
            {
                SummaryAmountTextBlock.Text = $"{amount:N2} DA";
                OpeningAmount = amount;
            }
            else
            {
                SummaryAmountTextBlock.Text = "0.00 DA";
                OpeningAmount = 0;
            }
        }

        private void OpenButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validation
                if (!decimal.TryParse(OpeningAmountTextBox.Text, out decimal amount) || amount < 0)
                {
                    MessageBox.Show("Le montant d'ouverture doit être un nombre positif.", "Validation", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    OpeningAmountTextBox.Focus();
                    return;
                }

                // Confirmation
                var result = MessageBox.Show(
                    $"Confirmer l'ouverture de la caisse avec un montant de {amount:N2} DA ?",
                    "Confirmation d'ouverture",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    OpeningAmount = amount;
                    Notes = NotesTextBox.Text.Trim();
                    
                    DialogResult = true;
                    Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur: {ex.Message}", 
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
