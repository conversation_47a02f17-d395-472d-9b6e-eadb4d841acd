using System;
using System.Collections.Generic;

namespace GestioDeStock.Models
{
    public class PurchaseInvoice : BaseEntity
    {
        public string InvoiceNumber { get; set; } = string.Empty;
        public string? SupplierInvoiceNumber { get; set; }
        public PurchaseInvoiceType InvoiceType { get; set; } = PurchaseInvoiceType.Achat;
        public int SupplierId { get; set; }
        public int WarehouseId { get; set; }
        public int UserId { get; set; }
        public DateTime InvoiceDate { get; set; } = DateTime.Now;
        public DateTime? DueDate { get; set; }
        public decimal SubtotalHt { get; set; } = 0.00m;
        public decimal TotalTva { get; set; } = 0.00m;
        public decimal TotalTtc { get; set; } = 0.00m;
        public decimal DiscountAmount { get; set; } = 0.00m;
        public decimal DiscountPercentage { get; set; } = 0.00m;
        public decimal TimbreAmount { get; set; } = 0.00m;
        public decimal FinalAmount { get; set; } = 0.00m;
        public decimal PaidAmount { get; set; } = 0.00m;
        public decimal RemainingAmount { get; set; } = 0.00m;
        public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.Espèce;
        public PaymentStatus PaymentStatus { get; set; } = PaymentStatus.NonPayé;
        public PurchaseInvoiceStatus Status { get; set; } = PurchaseInvoiceStatus.Brouillon;
        public string? Notes { get; set; }

        // Navigation properties
        public Supplier Supplier { get; set; } = null!;
        public Warehouse Warehouse { get; set; } = null!;
        public User User { get; set; } = null!;
        public List<PurchaseInvoiceDetail> Details { get; set; } = new List<PurchaseInvoiceDetail>();
    }

    public enum PurchaseInvoiceType
    {
        Achat,
        Retour
    }

    public enum PurchaseInvoiceStatus
    {
        Brouillon,
        Confirmé,
        Reçu,
        Annulé,
        Retourné
    }
}
