using Dapper;
using GestioDeStock.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace GestioDeStock.Data.Repositories
{
    public class UserRepository : BaseRepository<User>
    {
        public UserRepository(DatabaseConnection database) : base(database, "users")
        {
        }

        public override async Task<int> AddAsync(User entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                INSERT INTO users (username, password_hash, full_name, email, phone, role, is_active, created_by, updated_by)
                VALUES (@Username, @PasswordHash, @FullName, @Email, @Phone, @Role, @IsActive, @CreatedBy, @UpdatedBy);
                SELECT LAST_INSERT_ID();";
            
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(User entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE users SET 
                    username = @Username,
                    password_hash = @PasswordHash,
                    full_name = @FullName,
                    email = @Email,
                    phone = @Phone,
                    role = @Role,
                    is_active = @IsActive,
                    updated_at = NOW(),
                    updated_by = @UpdatedBy
                WHERE id = @Id";
            
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public override async Task<IEnumerable<User>> SearchAsync(string searchTerm)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT * FROM users 
                WHERE username LIKE @SearchTerm 
                   OR full_name LIKE @SearchTerm 
                   OR email LIKE @SearchTerm
                ORDER BY full_name";
            
            return await connection.QueryAsync<User>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<User?> GetByUsernameAsync(string username)
        {
            using var connection = _database.CreateConnection();
            var sql = "SELECT * FROM users WHERE username = @Username AND is_active = 1";
            return await connection.QueryFirstOrDefaultAsync<User>(sql, new { Username = username });
        }

        public async Task<bool> UpdateLastLoginAsync(int userId)
        {
            using var connection = _database.CreateConnection();
            var sql = "UPDATE users SET last_login = NOW() WHERE id = @UserId";
            var affectedRows = await connection.ExecuteAsync(sql, new { UserId = userId });
            return affectedRows > 0;
        }

        public async Task<IEnumerable<User>> GetActiveUsersAsync()
        {
            return await GetActiveAsync();
        }
    }
}
