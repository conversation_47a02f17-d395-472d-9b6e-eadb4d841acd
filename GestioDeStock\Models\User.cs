using System;

namespace GestioDeStock.Models
{
    public class User : BaseEntity
    {
        public string Username { get; set; } = string.Empty;
        public string PasswordHash { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public UserRole Role { get; set; } = UserRole.Vendeur;
        public bool IsActive { get; set; } = true;
        public DateTime? LastLogin { get; set; }
    }

    public enum UserRole
    {
        Administrateur,
        Vendeur,
        Gestionnaire,
        Caissier
    }
}
