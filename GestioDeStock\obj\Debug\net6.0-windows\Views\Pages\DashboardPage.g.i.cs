﻿#pragma checksum "..\..\..\..\..\Views\Pages\DashboardPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E11E4C5D74ADAEF9DD38E8FCB4E66ADDF6788C01"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GestioDeStock.Views.Pages {
    
    
    /// <summary>
    /// DashboardPage
    /// </summary>
    public partial class DashboardPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 20 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WelcomeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TodaySalesAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalProductsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LowStockTextBlock;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalClientsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NewSaleButton;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddProductButton;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NewPurchaseButton;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StockReportButton;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddClientButton;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CashSessionButton;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BackupButton;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsButton;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid RecentSalesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 212 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid LowStockDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GestioDeStock;V1.0.0.0;component/views/pages/dashboardpage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.WelcomeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TodaySalesAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TotalProductsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.LowStockTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.TotalClientsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.NewSaleButton = ((System.Windows.Controls.Button)(target));
            
            #line 114 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
            this.NewSaleButton.Click += new System.Windows.RoutedEventHandler(this.NewSaleButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.AddProductButton = ((System.Windows.Controls.Button)(target));
            
            #line 122 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
            this.AddProductButton.Click += new System.Windows.RoutedEventHandler(this.AddProductButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.NewPurchaseButton = ((System.Windows.Controls.Button)(target));
            
            #line 130 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
            this.NewPurchaseButton.Click += new System.Windows.RoutedEventHandler(this.NewPurchaseButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.StockReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 138 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
            this.StockReportButton.Click += new System.Windows.RoutedEventHandler(this.StockReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.AddClientButton = ((System.Windows.Controls.Button)(target));
            
            #line 146 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
            this.AddClientButton.Click += new System.Windows.RoutedEventHandler(this.AddClientButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.CashSessionButton = ((System.Windows.Controls.Button)(target));
            
            #line 154 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
            this.CashSessionButton.Click += new System.Windows.RoutedEventHandler(this.CashSessionButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.BackupButton = ((System.Windows.Controls.Button)(target));
            
            #line 162 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
            this.BackupButton.Click += new System.Windows.RoutedEventHandler(this.BackupButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.SettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 170 "..\..\..\..\..\Views\Pages\DashboardPage.xaml"
            this.SettingsButton.Click += new System.Windows.RoutedEventHandler(this.SettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.RecentSalesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 15:
            this.LowStockDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

