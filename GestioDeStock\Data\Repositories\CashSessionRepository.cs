using Dapper;
using GestioDeStock.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace GestioDeStock.Data.Repositories
{
    public class CashSessionRepository : BaseRepository<CashSession>
    {
        public CashSessionRepository(DatabaseConnection database) : base(database, "cash_sessions")
        {
        }

        public override async Task<int> AddAsync(CashSession entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                INSERT INTO cash_sessions (session_number, user_id, warehouse_id, opening_amount, 
                    closing_amount, total_sales, total_cash, total_card, total_check, total_credit, 
                    opened_at, closed_at, is_closed, notes, created_by, updated_by)
                VALUES (@SessionNumber, @UserId, @WarehouseId, @OpeningAmount, @ClosingAmount, 
                    @TotalSales, @TotalCash, @TotalCard, @TotalCheck, @TotalCredit, @OpenedAt, 
                    @ClosedAt, @IsClosed, @Notes, @CreatedBy, @UpdatedBy);
                SELECT LAST_INSERT_ID();";
            
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(CashSession entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE cash_sessions SET 
                    session_number = @SessionNumber,
                    user_id = @UserId,
                    warehouse_id = @WarehouseId,
                    opening_amount = @OpeningAmount,
                    closing_amount = @ClosingAmount,
                    total_sales = @TotalSales,
                    total_cash = @TotalCash,
                    total_card = @TotalCard,
                    total_check = @TotalCheck,
                    total_credit = @TotalCredit,
                    opened_at = @OpenedAt,
                    closed_at = @ClosedAt,
                    is_closed = @IsClosed,
                    notes = @Notes,
                    updated_at = NOW(),
                    updated_by = @UpdatedBy
                WHERE id = @Id";
            
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public override async Task<IEnumerable<CashSession>> SearchAsync(string searchTerm)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT cs.*, u.full_name as UserName, w.name as WarehouseName
                FROM cash_sessions cs
                LEFT JOIN users u ON cs.user_id = u.id
                LEFT JOIN warehouses w ON cs.warehouse_id = w.id
                WHERE cs.session_number LIKE @SearchTerm 
                   OR u.full_name LIKE @SearchTerm
                ORDER BY cs.opened_at DESC";
            
            return await connection.QueryAsync<CashSession>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<string> GetNextSessionNumberAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT CONCAT('CS', DATE_FORMAT(NOW(), '%Y%m%d'), 
                       LPAD(COALESCE(MAX(CAST(SUBSTRING(session_number, 11) AS UNSIGNED)), 0) + 1, 3, '0'))
                FROM cash_sessions 
                WHERE session_number LIKE CONCAT('CS', DATE_FORMAT(NOW(), '%Y%m%d'), '%')";
            
            return await connection.QuerySingleAsync<string>(sql);
        }

        public async Task<CashSession?> GetActiveSessionAsync(int userId, int warehouseId)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT * FROM cash_sessions 
                WHERE user_id = @UserId 
                  AND warehouse_id = @WarehouseId 
                  AND is_closed = 0 
                ORDER BY opened_at DESC 
                LIMIT 1";
            
            return await connection.QueryFirstOrDefaultAsync<CashSession>(sql, new { UserId = userId, WarehouseId = warehouseId });
        }

        public async Task<IEnumerable<CashSession>> GetSessionsByDateAsync(DateTime date, int? warehouseId = null)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT cs.*, u.full_name as UserName, w.name as WarehouseName
                FROM cash_sessions cs
                LEFT JOIN users u ON cs.user_id = u.id
                LEFT JOIN warehouses w ON cs.warehouse_id = w.id
                WHERE DATE(cs.opened_at) = @Date";
            
            if (warehouseId.HasValue)
            {
                sql += " AND cs.warehouse_id = @WarehouseId";
            }
            
            sql += " ORDER BY cs.opened_at DESC";
            
            return await connection.QueryAsync<CashSession>(sql, new { Date = date.Date, WarehouseId = warehouseId });
        }

        public async Task<IEnumerable<CashSession>> GetOpenSessionsAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT cs.*, u.full_name as UserName, w.name as WarehouseName
                FROM cash_sessions cs
                LEFT JOIN users u ON cs.user_id = u.id
                LEFT JOIN warehouses w ON cs.warehouse_id = w.id
                WHERE cs.is_closed = 0
                ORDER BY cs.opened_at DESC";
            
            return await connection.QueryAsync<CashSession>(sql);
        }

        public async Task<bool> CloseSessionAsync(int sessionId, decimal closingAmount, string? notes = null)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE cash_sessions 
                SET closing_amount = @ClosingAmount,
                    closed_at = NOW(),
                    is_closed = 1,
                    notes = @Notes,
                    updated_at = NOW()
                WHERE id = @SessionId";
            
            var affectedRows = await connection.ExecuteAsync(sql, new { SessionId = sessionId, ClosingAmount = closingAmount, Notes = notes });
            return affectedRows > 0;
        }

        public async Task<bool> UpdateSessionTotalsAsync(int sessionId, decimal totalSales, decimal totalCash, decimal totalCard, decimal totalCheck, decimal totalCredit)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE cash_sessions 
                SET total_sales = @TotalSales,
                    total_cash = @TotalCash,
                    total_card = @TotalCard,
                    total_check = @TotalCheck,
                    total_credit = @TotalCredit,
                    updated_at = NOW()
                WHERE id = @SessionId";
            
            var affectedRows = await connection.ExecuteAsync(sql, new 
            { 
                SessionId = sessionId, 
                TotalSales = totalSales, 
                TotalCash = totalCash, 
                TotalCard = totalCard, 
                TotalCheck = totalCheck, 
                TotalCredit = totalCredit 
            });
            
            return affectedRows > 0;
        }

        public async Task<decimal> GetTotalCashForPeriodAsync(DateTime fromDate, DateTime toDate, int? warehouseId = null)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT COALESCE(SUM(total_cash), 0) 
                FROM cash_sessions 
                WHERE DATE(opened_at) BETWEEN @FromDate AND @ToDate 
                  AND is_closed = 1";
            
            if (warehouseId.HasValue)
            {
                sql += " AND warehouse_id = @WarehouseId";
            }
            
            return await connection.QuerySingleAsync<decimal>(sql, new { FromDate = fromDate.Date, ToDate = toDate.Date, WarehouseId = warehouseId });
        }
    }
}
