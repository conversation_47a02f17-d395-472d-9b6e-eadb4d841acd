﻿using GestioDeStock.Services;
using GestioDeStock.Views.Pages;
using System.Windows;
using System.Windows.Threading;

namespace GestioDeStock;

public partial class MainWindow : Window
{
    private readonly AuthenticationService _authService;
    private readonly DispatcherTimer _timer;

    public MainWindow(AuthenticationService authService)
    {
        InitializeComponent();
        _authService = authService;

        // Initialiser l'interface
        InitializeUI();

        // Configurer le timer pour l'heure
        _timer = new DispatcherTimer();
        _timer.Interval = TimeSpan.FromSeconds(1);
        _timer.Tick += Timer_Tick;
        _timer.Start();

        // Charger la page d'accueil par défaut
        LoadDashboard();
    }

    private void InitializeUI()
    {
        if (_authService.CurrentUser != null)
        {
            UserNameTextBlock.Text = $"Bonjour, {_authService.CurrentUser.FullName}";

            // Configurer les permissions selon le rôle
            ConfigureMenuPermissions();
        }

        StatusTextBlock.Text = "Système prêt";
    }

    private void ConfigureMenuPermissions()
    {
        if (_authService.CurrentUser == null) return;

        // Masquer les boutons selon les permissions
        PurchasesButton.Visibility = _authService.CanAccessPurchases() ? Visibility.Visible : Visibility.Collapsed;
        ReportsButton.Visibility = _authService.CanAccessReports() ? Visibility.Visible : Visibility.Collapsed;
        SettingsButton.Visibility = _authService.CanAccessSettings() ? Visibility.Visible : Visibility.Collapsed;
    }

    private void Timer_Tick(object? sender, EventArgs e)
    {
        DateTimeTextBlock.Text = DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss");
    }

    private void LoadDashboard()
    {
        MainFrame.Navigate(new DashboardPage(_authService));
    }

    private void DashboardButton_Click(object sender, RoutedEventArgs e)
    {
        LoadDashboard();
        StatusTextBlock.Text = "Tableau de bord";
    }

    private void SalesButton_Click(object sender, RoutedEventArgs e)
    {
        if (_authService.CanAccessSales())
        {
            MainFrame.Navigate(new SalesPage(_authService));
            StatusTextBlock.Text = "Point de vente";
        }
    }

    private void ProductsButton_Click(object sender, RoutedEventArgs e)
    {
        if (_authService.CanManageProducts())
        {
            MainFrame.Navigate(new ProductsPage(_authService));
            StatusTextBlock.Text = "Gestion des produits";
        }
    }

    private void PurchasesButton_Click(object sender, RoutedEventArgs e)
    {
        if (_authService.CanAccessPurchases())
        {
            MainFrame.Navigate(new PurchasesPage(_authService));
            StatusTextBlock.Text = "Gestion des achats";
        }
    }

    private void StockButton_Click(object sender, RoutedEventArgs e)
    {
        MainFrame.Navigate(new StockPage(_authService));
        StatusTextBlock.Text = "Gestion du stock";
    }

    private void ClientsButton_Click(object sender, RoutedEventArgs e)
    {
        MainFrame.Navigate(new ClientsPage(_authService));
        StatusTextBlock.Text = "Gestion des clients";
    }

    private void SuppliersButton_Click(object sender, RoutedEventArgs e)
    {
        MainFrame.Navigate(new SuppliersPage(_authService));
        StatusTextBlock.Text = "Gestion des fournisseurs";
    }

    private void CashButton_Click(object sender, RoutedEventArgs e)
    {
        MainFrame.Navigate(new CashPage(_authService));
        StatusTextBlock.Text = "Gestion de la caisse";
    }

    private void ReportsButton_Click(object sender, RoutedEventArgs e)
    {
        if (_authService.CanAccessReports())
        {
            MainFrame.Navigate(new ReportsPage(_authService));
            StatusTextBlock.Text = "Rapports et statistiques";
        }
    }

    private void SettingsButton_Click(object sender, RoutedEventArgs e)
    {
        if (_authService.CanAccessSettings())
        {
            MainFrame.Navigate(new SettingsPage(_authService));
            StatusTextBlock.Text = "Paramètres du système";
        }
    }

    private void LogoutButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("Êtes-vous sûr de vouloir vous déconnecter ?",
                                   "Confirmation",
                                   MessageBoxButton.YesNo,
                                   MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            _authService.Logout();
            _timer.Stop();

            var loginWindow = new Views.LoginWindow();
            loginWindow.Show();
            this.Close();
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        _timer?.Stop();
        base.OnClosed(e);
    }
}