using System.Collections.Generic;

namespace GestioDeStock.Models
{
    public class Unit : BaseEntity
    {
        public string Name { get; set; } = string.Empty;
        public string Symbol { get; set; } = string.Empty;
        public UnitType Type { get; set; } = UnitType.Unité;
        public bool IsActive { get; set; } = true;

        // Navigation properties
        public List<Product> Products { get; set; } = new List<Product>();
        public List<ProductUnit> ProductUnits { get; set; } = new List<ProductUnit>();
    }

    public enum UnitType
    {
        Poids,
        Volume,
        Unité,
        Longueur
    }
}
