# دليل التثبيت والتشغيل - Installation Guide

## المتطلبات الأساسية

### 1. متطلبات النظام
- Windows 10/11 (64-bit)
- .NET 6.0 Runtime أو أحدث
- MySQL Server 8.0 أو أحدث
- ذاكرة RAM: 4 GB كحد أدنى (8 GB مُوصى به)
- مساحة القرص الصلب: 500 MB للتطبيق + مساحة لقاعدة البيانات

### 2. تحميل وتثبيت المتطلبات

#### تثبيت .NET 6.0
1. اذهب إلى: https://dotnet.microsoft.com/download/dotnet/6.0
2. حمّل ".NET 6.0 Desktop Runtime" للـ Windows x64
3. قم بتثبيته

#### تثبيت MySQL Server
1. اذهب إلى: https://dev.mysql.com/downloads/mysql/
2. حمّل MySQL Community Server 8.0
3. قم بتثبيته مع تذكر كلمة مرور المستخدم root

## خطوات التثبيت

### الخطوة 1: إعداد قاعدة البيانات

1. **فتح MySQL Command Line أو MySQL Workbench**

2. **إنشاء قاعدة البيانات:**
```sql
CREATE DATABASE gestion_stock CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. **تشغيل ملف قاعدة البيانات:**
```bash
mysql -u root -p gestion_stock < database.sql
```

أو من خلال MySQL Workbench:
- افتح ملف `database.sql`
- قم بتشغيل جميع الاستعلامات

### الخطوة 2: تكوين التطبيق

1. **تعديل ملف الاتصال:**
افتح ملف `GestioDeStock/App.config` وعدّل سلسلة الاتصال:

```xml
<connectionStrings>
    <add name="DefaultConnection" 
         connectionString="Server=localhost;Database=gestion_stock;Uid=root;Pwd=YOUR_PASSWORD;CharSet=utf8mb4;" 
         providerName="MySql.Data.MySqlClient" />
</connectionStrings>
```

استبدل `YOUR_PASSWORD` بكلمة مرور MySQL الخاصة بك.

### الخطوة 3: بناء وتشغيل التطبيق

#### الطريقة الأولى: من خلال Visual Studio
1. افتح ملف `GestioDeStock.sln` في Visual Studio
2. اضغط F5 أو "Start Debugging"

#### الطريقة الثانية: من خلال Command Line
```bash
cd GestioDeStock
dotnet restore
dotnet build
dotnet run
```

## التحقق من التثبيت

### 1. تسجيل الدخول
- **اسم المستخدم:** admin
- **كلمة المرور:** admin

### 2. التحقق من الاتصال بقاعدة البيانات
- إذا تم تسجيل الدخول بنجاح، فهذا يعني أن الاتصال يعمل
- تحقق من ظهور لوحة التحكم

### 3. اختبار الوظائف الأساسية
- جرب الانتقال بين القوائم المختلفة
- تأكد من عدم ظهور رسائل خطأ

## حل المشاكل الشائعة

### مشكلة: "Could not connect to database"
**الحل:**
1. تأكد من تشغيل MySQL Server
2. تحقق من صحة بيانات الاتصال في App.config
3. تأكد من وجود قاعدة البيانات `gestion_stock`

### مشكلة: "Login failed"
**الحل:**
1. تأكد من تشغيل ملف database.sql بالكامل
2. تحقق من وجود المستخدم الافتراضي:
```sql
SELECT * FROM users WHERE username = 'admin';
```

### مشكلة: "MaterialDesign themes not loading"
**الحل:**
1. تأكد من تثبيت جميع الحزم:
```bash
dotnet restore
```

### مشكلة: "MySQL connection timeout"
**الحل:**
1. زيادة timeout في سلسلة الاتصال:
```xml
connectionString="Server=localhost;Database=gestion_stock;Uid=root;Pwd=password;CharSet=utf8mb4;Connection Timeout=30;"
```

## إعدادات إضافية

### تكوين النسخ الاحتياطية
1. أنشئ مجلد للنسخ الاحتياطية: `C:\Backups\GestionStock`
2. عدّل المسار في App.config إذا لزم الأمر

### تكوين التقارير
1. أنشئ مجلد للتقارير: `C:\Reports\GestionStock`
2. تأكد من صلاحيات الكتابة

### تكوين الطابعة الحرارية
1. تثبيت تعريف الطابعة
2. تكوين إعدادات الطباعة من قائمة الإعدادات

## الأمان

### تغيير كلمة مرور المدير
1. سجل دخول كمدير
2. اذهب إلى الإعدادات > إدارة المستخدمين
3. غيّر كلمة مرور المستخدم admin

### إنشاء مستخدمين جدد
1. من قائمة الإعدادات
2. أضف مستخدمين بصلاحيات مختلفة حسب الحاجة

## الصيانة

### النسخ الاحتياطية اليومية
```bash
mysqldump -u root -p gestion_stock > backup_$(date +%Y%m%d).sql
```

### تنظيف السجلات القديمة
قم بتنظيف سجلات تسجيل الدخول القديمة دورياً:
```sql
DELETE FROM login_history WHERE login_time < DATE_SUB(NOW(), INTERVAL 6 MONTH);
```

## الدعم الفني

في حالة مواجهة مشاكل:
1. تحقق من ملفات السجل (logs)
2. تأكد من تحديث جميع المكونات
3. راجع قسم حل المشاكل أعلاه

---

**ملاحظة:** تأكد من إجراء نسخة احتياطية من البيانات قبل أي تحديث أو تعديل على النظام.
