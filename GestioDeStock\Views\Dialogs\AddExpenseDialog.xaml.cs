using GestioDeStock.Data;
using GestioDeStock.Data.Repositories;
using GestioDeStock.Models;
using GestioDeStock.Services;
using System.Windows;
using System.Windows.Controls;

namespace GestioDeStock.Views.Dialogs
{
    public partial class AddExpenseDialog : Window
    {
        private readonly ExpenseRepository _expenseRepository;
        private readonly ExpenseCategoryRepository _categoryRepository;
        private readonly BankAccountRepository _bankAccountRepository;
        private readonly AuthenticationService _authService;

        public Expense? NewExpense { get; private set; }

        public AddExpenseDialog(AuthenticationService authService)
        {
            InitializeComponent();
            _authService = authService;
            
            var config = ConfigurationService.Instance;
            var database = new DatabaseConnection(config.GetConnectionString());
            _expenseRepository = new ExpenseRepository(database);
            _categoryRepository = new ExpenseCategoryRepository(database);
            _bankAccountRepository = new BankAccountRepository(database);
            
            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                // Load categories for expenses (default)
                await LoadCategories(ExpenseType.Dépense);
                
                // Load bank accounts
                var bankAccounts = await _bankAccountRepository.GetActiveAccountsAsync();
                BankAccountComboBox.ItemsSource = bankAccounts;
                BankAccountComboBox.DisplayMemberPath = "AccountName";
                BankAccountComboBox.SelectedValuePath = "Id";
                
                // Set default date
                TransactionDatePicker.SelectedDate = DateTime.Today;
                
                UpdateUI();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement: {ex.Message}", 
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadCategories(ExpenseType type)
        {
            try
            {
                var categories = await _categoryRepository.GetCategoriesByTypeAsync(type);
                CategoryComboBox.ItemsSource = categories;
                CategoryComboBox.DisplayMemberPath = "Name";
                CategoryComboBox.SelectedValuePath = "Id";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des catégories: {ex.Message}", 
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void TypeRadioButton_Checked(object sender, RoutedEventArgs e)
        {
            if (ExpenseRadioButton.IsChecked == true)
            {
                await LoadCategories(ExpenseType.Dépense);
                HeaderIcon.Kind = MaterialDesignThemes.Wpf.PackIconKind.CashMinus;
                HeaderTextBlock.Text = "Nouvelle Dépense";
                SummaryLabelTextBlock.Text = "Total Dépense:";
            }
            else
            {
                await LoadCategories(ExpenseType.Recette);
                HeaderIcon.Kind = MaterialDesignThemes.Wpf.PackIconKind.CashPlus;
                HeaderTextBlock.Text = "Nouvelle Recette";
                SummaryLabelTextBlock.Text = "Total Recette:";
            }
        }

        private void PaymentMethodComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (PaymentMethodComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var paymentMethod = selectedItem.Content.ToString();
                
                // Show/hide bank account based on payment method
                if (paymentMethod == "Espèces")
                {
                    BankAccountComboBox.Visibility = Visibility.Collapsed;
                    CheckDetailsGrid.Visibility = Visibility.Collapsed;
                }
                else if (paymentMethod == "Chèque")
                {
                    BankAccountComboBox.Visibility = Visibility.Visible;
                    CheckDetailsGrid.Visibility = Visibility.Visible;
                    CheckDatePicker.SelectedDate = DateTime.Today;
                }
                else
                {
                    BankAccountComboBox.Visibility = Visibility.Visible;
                    CheckDetailsGrid.Visibility = Visibility.Collapsed;
                }
            }
        }

        private void AmountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateUI();
        }

        private void UpdateUI()
        {
            // Update summary
            if (decimal.TryParse(AmountTextBox.Text, out decimal amount) && amount > 0)
            {
                SummaryAmountTextBlock.Text = $"{amount:N2} DA";
            }
            else
            {
                SummaryAmountTextBlock.Text = "0.00 DA";
            }
            
            // Enable/disable save button
            SaveButton.IsEnabled = !string.IsNullOrWhiteSpace(DescriptionTextBox.Text) &&
                                  CategoryComboBox.SelectedValue != null &&
                                  decimal.TryParse(AmountTextBox.Text, out decimal amt) && amt > 0;
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validation
                if (string.IsNullOrWhiteSpace(DescriptionTextBox.Text))
                {
                    MessageBox.Show("La description est obligatoire.", "Validation", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    DescriptionTextBox.Focus();
                    return;
                }

                if (CategoryComboBox.SelectedValue == null)
                {
                    MessageBox.Show("Veuillez sélectionner une catégorie.", "Validation", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    CategoryComboBox.Focus();
                    return;
                }

                if (!decimal.TryParse(AmountTextBox.Text, out decimal amount) || amount <= 0)
                {
                    MessageBox.Show("Le montant doit être un nombre positif.", "Validation", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    AmountTextBox.Focus();
                    return;
                }

                // Get payment method
                var paymentMethod = ((ComboBoxItem)PaymentMethodComboBox.SelectedItem).Content.ToString() ?? "Espèces";
                
                // Validate bank account for non-cash payments
                if (paymentMethod != "Espèces" && BankAccountComboBox.SelectedValue == null)
                {
                    MessageBox.Show("Veuillez sélectionner un compte bancaire.", "Validation", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    BankAccountComboBox.Focus();
                    return;
                }

                // Generate reference number
                var expenseType = ExpenseRadioButton.IsChecked == true ? ExpenseType.Dépense : ExpenseType.Recette;
                var referenceNumber = await _expenseRepository.GetNextReferenceNumberAsync(expenseType);

                // Create expense
                var newExpense = new Expense
                {
                    ReferenceNumber = referenceNumber,
                    CategoryId = (int)CategoryComboBox.SelectedValue,
                    Type = expenseType,
                    Description = DescriptionTextBox.Text.Trim(),
                    Amount = amount,
                    PaymentMethod = paymentMethod,
                    BankAccountId = paymentMethod != "Espèces" ? (int?)BankAccountComboBox.SelectedValue : null,
                    CheckNumber = paymentMethod == "Chèque" ? CheckNumberTextBox.Text.Trim() : null,
                    CheckDate = paymentMethod == "Chèque" ? CheckDatePicker.SelectedDate : null,
                    TransactionDate = TransactionDatePicker.SelectedDate ?? DateTime.Today,
                    Notes = NotesTextBox.Text.Trim(),
                    CreatedBy = _authService.CurrentUser?.Id ?? 0,
                    UpdatedBy = _authService.CurrentUser?.Id ?? 0
                };

                // Save to database
                var expenseId = await _expenseRepository.AddAsync(newExpense);
                newExpense.Id = expenseId;

                NewExpense = newExpense;
                
                var typeText = expenseType == ExpenseType.Dépense ? "Dépense" : "Recette";
                MessageBox.Show($"{typeText} enregistrée avec succès!", "Succès", 
                              MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'enregistrement: {ex.Message}", 
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
