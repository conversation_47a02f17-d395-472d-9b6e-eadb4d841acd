@echo off
echo ========================================
echo   Gestion de Stock - Construction Release
echo ========================================
echo.

cd GestioDeStock

echo Nettoyage des fichiers temporaires...
dotnet clean

echo.
echo Restauration des dependances...
dotnet restore

echo.
echo Construction en mode Release...
dotnet build --configuration Release

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Publication de l'application...
    dotnet publish --configuration Release --output ..\Release --self-contained false
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ========================================
        echo   Construction terminee avec succes!
        echo ========================================
        echo.
        echo L'application est disponible dans le dossier 'Release'
        echo.
        echo Pour executer l'application:
        echo   cd Release
        echo   GestioDeStock.exe
        echo.
    ) else (
        echo.
        echo Erreur lors de la publication!
    )
) else (
    echo.
    echo Erreur lors de la construction!
)

pause
