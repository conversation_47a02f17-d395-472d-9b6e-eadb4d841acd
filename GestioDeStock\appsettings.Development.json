{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=gestion_stock;Uid=root;Pwd=;CharSet=utf8mb4;Connection Timeout=30;"}, "AppSettings": {"CompanyName": "Mon Épicerie - DEV", "ApplicationVersion": "1.0.0-dev", "BackupPath": "C:\\Dev\\Backups\\GestionStock", "ReportsPath": "C:\\Dev\\Reports\\GestionStock", "AutoBackupEnabled": false}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Information", "Microsoft.Hosting.Lifetime": "Information"}, "EnableFileLogging": true, "LogFilePath": "Logs\\dev.log"}, "SecuritySettings": {"PasswordMinLength": 3, "EnableAuditLog": true}, "UISettings": {"ShowAnimations": true, "AutoRefreshInterval": 10}}