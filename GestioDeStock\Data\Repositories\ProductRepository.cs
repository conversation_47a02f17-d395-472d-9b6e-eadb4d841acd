using Dapper;
using GestioDeStock.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace GestioDeStock.Data.Repositories
{
    public class ProductRepository : BaseRepository<Product>
    {
        public ProductRepository(DatabaseConnection database) : base(database, "products")
        {
        }

        public override async Task<int> AddAsync(Product entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                INSERT INTO products (code, name, description, category_id, brand_id, unit_id, image, 
                    expiry_date, prix_achat, prix_vente_ht, prix_vente_ttc, tva_rate, pmp, 
                    pieces_per_fardeau, prix_fardeau, min_stock_alert, max_stock_alert, 
                    is_active, is_archived, is_scale_product, scale_prefix, created_by, updated_by)
                VALUES (@Code, @Name, @Description, @CategoryId, @BrandId, @UnitId, @Image,
                    @ExpiryDate, @PrixAchat, @PrixVenteHt, @PrixVenteTtc, @TvaRate, @Pmp,
                    @PiecesPerFardeau, @PrixFardeau, @MinStockAlert, @MaxStockAlert,
                    @IsActive, @IsArchived, @IsScaleProduct, @ScalePrefix, @CreatedBy, @UpdatedBy);
                SELECT LAST_INSERT_ID();";
            
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Product entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE products SET 
                    code = @Code,
                    name = @Name,
                    description = @Description,
                    category_id = @CategoryId,
                    brand_id = @BrandId,
                    unit_id = @UnitId,
                    image = @Image,
                    expiry_date = @ExpiryDate,
                    prix_achat = @PrixAchat,
                    prix_vente_ht = @PrixVenteHt,
                    prix_vente_ttc = @PrixVenteTtc,
                    tva_rate = @TvaRate,
                    pmp = @Pmp,
                    pieces_per_fardeau = @PiecesPerFardeau,
                    prix_fardeau = @PrixFardeau,
                    min_stock_alert = @MinStockAlert,
                    max_stock_alert = @MaxStockAlert,
                    is_active = @IsActive,
                    is_archived = @IsArchived,
                    is_scale_product = @IsScaleProduct,
                    scale_prefix = @ScalePrefix,
                    updated_at = NOW(),
                    updated_by = @UpdatedBy
                WHERE id = @Id";
            
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public override async Task<IEnumerable<Product>> SearchAsync(string searchTerm)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT p.*, c.name as CategoryName, b.name as BrandName, u.name as UnitName
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN brands b ON p.brand_id = b.id
                LEFT JOIN units u ON p.unit_id = u.id
                WHERE p.code LIKE @SearchTerm 
                   OR p.name LIKE @SearchTerm 
                   OR c.name LIKE @SearchTerm
                   OR b.name LIKE @SearchTerm
                ORDER BY p.name";
            
            return await connection.QueryAsync<Product>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<Product?> GetByCodeAsync(string code)
        {
            using var connection = _database.CreateConnection();
            var sql = "SELECT * FROM products WHERE code = @Code AND is_active = 1";
            return await connection.QueryFirstOrDefaultAsync<Product>(sql, new { Code = code });
        }

        public async Task<Product?> GetByBarcodeAsync(string barcode)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT p.* FROM products p
                INNER JOIN product_barcodes pb ON p.id = pb.product_id
                WHERE pb.barcode = @Barcode AND p.is_active = 1";
            return await connection.QueryFirstOrDefaultAsync<Product>(sql, new { Barcode = barcode });
        }

        public async Task<IEnumerable<Product>> GetActiveProductsAsync()
        {
            return await GetActiveAsync();
        }

        public async Task<IEnumerable<Product>> GetLowStockProductsAsync(int warehouseId)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT p.*, ps.quantity_available
                FROM products p
                INNER JOIN product_stock ps ON p.id = ps.product_id
                WHERE ps.warehouse_id = @WarehouseId 
                  AND ps.quantity_available <= p.min_stock_alert
                  AND p.is_active = 1
                ORDER BY p.name";
            
            return await connection.QueryAsync<Product>(sql, new { WarehouseId = warehouseId });
        }
    }
}
