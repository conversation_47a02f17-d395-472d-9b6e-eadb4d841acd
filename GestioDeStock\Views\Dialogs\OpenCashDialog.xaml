<Window x:Class="GestioDeStock.Views.Dialogs.OpenCashDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Ouverture de Caisse" 
        Height="350" Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="CashRegister" Width="48" Height="48" 
                                   Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                   HorizontalAlignment="Center" Margin="0,0,0,10"/>
            <TextBlock Text="Ouverture de Session de Caisse" FontSize="18" FontWeight="Bold" 
                     HorizontalAlignment="Center"/>
            <TextBlock x:Name="SessionNumberTextBlock" Text="Session: CS20240101001" FontSize="14" 
                     HorizontalAlignment="Center" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
        </StackPanel>
        
        <!-- Form -->
        <StackPanel Grid.Row="1">
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <TextBox x:Name="UserTextBox" Grid.Column="0"
                       materialDesign:HintAssist.Hint="Utilisateur"
                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                       Margin="0,0,10,0"
                       IsReadOnly="True"/>
                
                <TextBox x:Name="WarehouseTextBox" Grid.Column="1"
                       materialDesign:HintAssist.Hint="Magasin"
                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                       Margin="10,0,0,0"
                       IsReadOnly="True"/>
            </Grid>
            
            <TextBox x:Name="OpeningAmountTextBox"
                   materialDesign:HintAssist.Hint="Montant d'ouverture (DA)"
                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                   Text="0.00"
                   TextChanged="OpeningAmountTextBox_TextChanged"/>
            
            <TextBox x:Name="NotesTextBox"
                   materialDesign:HintAssist.Hint="Notes (optionnel)"
                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                   AcceptsReturn="True"
                   TextWrapping="Wrap"
                   MinLines="2"
                   MaxLines="3"/>
            
            <Separator/>
            
            <!-- Summary -->
            <StackPanel>
                <TextBlock Text="Résumé de l'ouverture:" FontSize="14" FontWeight="Medium" Margin="0,0,0,10"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <TextBlock Grid.Row="0" Grid.Column="0" Text="Date et heure:" FontSize="12"/>
                    <TextBlock x:Name="DateTimeTextBlock" Grid.Row="0" Grid.Column="1" Text="" FontSize="12" FontWeight="Bold"/>
                    
                    <TextBlock Grid.Row="1" Grid.Column="0" Text="Montant d'ouverture:" FontSize="12"/>
                    <TextBlock x:Name="SummaryAmountTextBlock" Grid.Row="1" Grid.Column="1" Text="0.00 DA" FontSize="12" FontWeight="Bold"/>
                    
                    <TextBlock Grid.Row="2" Grid.Column="0" Text="Statut:" FontSize="12"/>
                    <TextBlock Grid.Row="2" Grid.Column="1" Text="Prêt à ouvrir" FontSize="12" FontWeight="Bold" Foreground="#4CAF50"/>
                </Grid>
            </StackPanel>
            
        </StackPanel>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="OpenButton" 
                  Style="{StaticResource MaterialDesignRaisedButton}"
                  Content="OUVRIR CAISSE" 
                  Margin="0,0,10,0"
                  Click="OpenButton_Click">
                <Button.ContentTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="CashRegister" Width="16" Height="16" 
                                                   VerticalAlignment="Center" Margin="0,0,5,0"/>
                            <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </Button.ContentTemplate>
            </Button>
            
            <Button x:Name="CancelButton" 
                  Style="{StaticResource MaterialDesignOutlinedButton}"
                  Content="ANNULER" 
                  Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
