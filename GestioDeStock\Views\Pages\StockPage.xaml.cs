using GestioDeStock.Data;
using GestioDeStock.Data.Repositories;
using GestioDeStock.Models;
using GestioDeStock.Services;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;

namespace GestioDeStock.Views.Pages
{
    public partial class StockPage : Page
    {
        private readonly AuthenticationService _authService;
        private readonly ProductStockRepository _stockRepository;
        private readonly WarehouseRepository _warehouseRepository;
        private ObservableCollection<StockViewModel> _stockItems;
        private List<StockViewModel> _allStockItems;

        public StockPage(AuthenticationService authService)
        {
            InitializeComponent();
            _authService = authService;

            var config = ConfigurationService.Instance;
            var database = new DatabaseConnection(config.GetConnectionString());
            _stockRepository = new ProductStockRepository(database);
            _warehouseRepository = new WarehouseRepository(database);

            _stockItems = new ObservableCollection<StockViewModel>();
            _allStockItems = new List<StockViewModel>();

            StockDataGrid.ItemsSource = _stockItems;

            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                // Load warehouses for filter
                var warehouses = await _warehouseRepository.GetActiveWarehousesAsync();
                WarehouseComboBox.ItemsSource = warehouses;
                WarehouseComboBox.DisplayMemberPath = "Name";
                WarehouseComboBox.SelectedValuePath = "Id";

                // Set default filters
                StockStatusComboBox.SelectedIndex = 0; // All

                // Load stock data
                await LoadStock();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des données: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadStock()
        {
            try
            {
                var stockData = await _stockRepository.GetAllAsync();

                _allStockItems = stockData.Select(s => new StockViewModel
                {
                    ProductId = s.ProductId,
                    WarehouseId = s.WarehouseId,
                    ProductCode = s.ProductCode ?? "",
                    ProductName = s.ProductName ?? "",
                    WarehouseName = s.WarehouseName ?? "",
                    QuantityAvailable = s.QuantityAvailable,
                    QuantityReserved = s.QuantityReserved,
                    AverageCost = s.AverageCost,
                    MinStockAlert = s.MinStockAlert,
                    StockValue = s.QuantityAvailable * s.AverageCost,
                    StockStatus = GetStockStatus(s.QuantityAvailable, s.MinStockAlert, s.MaxStockAlert)
                }).ToList();

                ApplyFilters();
                UpdateSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement du stock: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GetStockStatus(decimal available, decimal minAlert, decimal? maxAlert)
        {
            if (available <= 0) return "Rupture";
            if (available <= minAlert) return "Faible";
            if (maxAlert.HasValue && available >= maxAlert.Value) return "Élevé";
            return "Normal";
        }

        private void ApplyFilters()
        {
            var filteredStock = _allStockItems.AsEnumerable();

            // Search filter
            if (!string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                var searchTerm = SearchTextBox.Text.ToLower();
                filteredStock = filteredStock.Where(s =>
                    s.ProductName.ToLower().Contains(searchTerm) ||
                    s.ProductCode.ToLower().Contains(searchTerm));
            }

            // Warehouse filter
            if (WarehouseComboBox.SelectedValue != null)
            {
                var warehouseId = (int)WarehouseComboBox.SelectedValue;
                filteredStock = filteredStock.Where(s => s.WarehouseId == warehouseId);
            }

            // Stock status filter
            if (StockStatusComboBox.SelectedIndex > 0)
            {
                var statusText = ((ComboBoxItem)StockStatusComboBox.SelectedItem).Content.ToString();
                switch (statusText)
                {
                    case "Stock Normal":
                        filteredStock = filteredStock.Where(s => s.StockStatus == "Normal");
                        break;
                    case "Stock Faible":
                        filteredStock = filteredStock.Where(s => s.StockStatus == "Faible");
                        break;
                    case "Rupture de Stock":
                        filteredStock = filteredStock.Where(s => s.StockStatus == "Rupture");
                        break;
                    case "Stock Élevé":
                        filteredStock = filteredStock.Where(s => s.StockStatus == "Élevé");
                        break;
                }
            }

            var results = filteredStock.ToList();

            // Update UI
            _stockItems.Clear();
            foreach (var item in results)
            {
                _stockItems.Add(item);
            }

            ResultsCountTextBlock.Text = $"{results.Count} produits trouvés";
        }

        private void UpdateSummary()
        {
            var totalProducts = _allStockItems.Count;
            var stockValue = _allStockItems.Sum(s => s.StockValue);
            var lowStock = _allStockItems.Count(s => s.StockStatus == "Faible");
            var outOfStock = _allStockItems.Count(s => s.StockStatus == "Rupture");

            TotalProductsTextBlock.Text = totalProducts.ToString();
            StockValueTextBlock.Text = $"{stockValue:N2} DA";
            LowStockTextBlock.Text = lowStock.ToString();
            OutOfStockTextBlock.Text = outOfStock.ToString();
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void WarehouseComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void StockStatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadStock();
        }

        private void StockAdjustmentButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Open stock adjustment dialog
            MessageBox.Show("Ajustement de stock à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void InventoryButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Open inventory dialog
            MessageBox.Show("Inventaire à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AdjustStockButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is StockViewModel stock)
            {
                // TODO: Open stock adjustment dialog for specific product
                MessageBox.Show($"Ajuster le stock de {stock.ProductName}", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void HistoryButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is StockViewModel stock)
            {
                // TODO: Open stock movement history dialog
                MessageBox.Show($"Historique des mouvements de {stock.ProductName}", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void TransferButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is StockViewModel stock)
            {
                // TODO: Open stock transfer dialog
                MessageBox.Show($"Transfert de stock pour {stock.ProductName}", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void StockDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (StockDataGrid.SelectedItem is StockViewModel stock)
            {
                // TODO: Open stock details dialog
                MessageBox.Show($"Détails du stock de {stock.ProductName}", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement export functionality
            MessageBox.Show("Fonctionnalité d'export à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement print functionality
            MessageBox.Show("Fonctionnalité d'impression à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    // ViewModel for stock display
    public class StockViewModel : INotifyPropertyChanged
    {
        public int ProductId { get; set; }
        public int WarehouseId { get; set; }
        public string ProductCode { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public string WarehouseName { get; set; } = string.Empty;
        public decimal QuantityAvailable { get; set; }
        public decimal QuantityReserved { get; set; }
        public decimal AverageCost { get; set; }
        public decimal MinStockAlert { get; set; }
        public decimal StockValue { get; set; }
        public string StockStatus { get; set; } = string.Empty;

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
