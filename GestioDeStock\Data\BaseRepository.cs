using Dapper;
using GestioDeStock.Models;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace GestioDeStock.Data
{
    public abstract class BaseRepository<T> : IRepository<T> where T : class
    {
        protected readonly DatabaseConnection _database;
        protected readonly string _tableName;

        protected BaseRepository(DatabaseConnection database, string tableName)
        {
            _database = database;
            _tableName = tableName;
        }

        public virtual async Task<IEnumerable<T>> GetAllAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = $"SELECT * FROM {_tableName} ORDER BY id";
            return await connection.QueryAsync<T>(sql);
        }

        public virtual async Task<T?> GetByIdAsync(int id)
        {
            using var connection = _database.CreateConnection();
            var sql = $"SELECT * FROM {_tableName} WHERE id = @Id";
            return await connection.QueryFirstOrDefaultAsync<T>(sql, new { Id = id });
        }

        public abstract Task<int> AddAsync(T entity);
        public abstract Task<bool> UpdateAsync(T entity);

        public virtual async Task<bool> DeleteAsync(int id)
        {
            using var connection = _database.CreateConnection();
            var sql = $"DELETE FROM {_tableName} WHERE id = @Id";
            var affectedRows = await connection.ExecuteAsync(sql, new { Id = id });
            return affectedRows > 0;
        }

        public abstract Task<IEnumerable<T>> SearchAsync(string searchTerm);

        public virtual async Task<int> CountAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = $"SELECT COUNT(*) FROM {_tableName}";
            return await connection.QuerySingleAsync<int>(sql);
        }

        public async Task<IEnumerable<T>> GetActiveAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = $"SELECT * FROM {_tableName} WHERE is_active = 1 ORDER BY id";
            return await connection.QueryAsync<T>(sql);
        }
    }
}
