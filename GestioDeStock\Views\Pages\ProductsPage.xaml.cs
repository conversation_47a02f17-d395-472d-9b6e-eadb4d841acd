using GestioDeStock.Data;
using GestioDeStock.Data.Repositories;
using GestioDeStock.Models;
using GestioDeStock.Services;
using GestioDeStock.Views.Dialogs;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace GestioDeStock.Views.Pages
{
    public partial class ProductsPage : Page
    {
        private readonly AuthenticationService _authService;
        private readonly ProductRepository _productRepository;
        private readonly CategoryRepository _categoryRepository;
        private ObservableCollection<Product> _products;
        private List<Product> _allProducts;
        private int _currentPage = 1;
        private int _pageSize = 50;
        private int _totalPages = 1;

        public ProductsPage(AuthenticationService authService)
        {
            InitializeComponent();
            _authService = authService;

            var config = ConfigurationService.Instance;
            var database = new DatabaseConnection(config.GetConnectionString());
            _productRepository = new ProductRepository(database);
            _categoryRepository = new CategoryRepository(database);

            _products = new ObservableCollection<Product>();
            _allProducts = new List<Product>();

            ProductsDataGrid.ItemsSource = _products;

            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                // Load categories for filter
                var categories = await _categoryRepository.GetActiveCategoriesAsync();
                CategoryComboBox.ItemsSource = categories;
                CategoryComboBox.DisplayMemberPath = "Name";
                CategoryComboBox.SelectedValuePath = "Id";

                // Set default status filter
                StatusComboBox.SelectedIndex = 1; // Active products

                // Load products
                await LoadProducts();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des données: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadProducts()
        {
            try
            {
                _allProducts = (await _productRepository.GetAllAsync()).ToList();
                ApplyFilters();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des produits: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplyFilters()
        {
            var filteredProducts = _allProducts.AsEnumerable();

            // Search filter
            if (!string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                var searchTerm = SearchTextBox.Text.ToLower();
                filteredProducts = filteredProducts.Where(p =>
                    p.Name.ToLower().Contains(searchTerm) ||
                    p.Code.ToLower().Contains(searchTerm) ||
                    (p.Description?.ToLower().Contains(searchTerm) ?? false));
            }

            // Category filter
            if (CategoryComboBox.SelectedValue != null)
            {
                var categoryId = (int)CategoryComboBox.SelectedValue;
                filteredProducts = filteredProducts.Where(p => p.CategoryId == categoryId);
            }

            // Status filter
            switch (StatusComboBox.SelectedIndex)
            {
                case 1: // Active
                    filteredProducts = filteredProducts.Where(p => p.IsActive && !p.IsArchived);
                    break;
                case 2: // Inactive
                    filteredProducts = filteredProducts.Where(p => !p.IsActive);
                    break;
                case 3: // Archived
                    filteredProducts = filteredProducts.Where(p => p.IsArchived);
                    break;
                default: // All
                    break;
            }

            var results = filteredProducts.ToList();

            // Update pagination
            _totalPages = (int)Math.Ceiling((double)results.Count / _pageSize);
            _currentPage = Math.Min(_currentPage, Math.Max(1, _totalPages));

            // Apply pagination
            var pagedResults = results
                .Skip((_currentPage - 1) * _pageSize)
                .Take(_pageSize)
                .ToList();

            // Update UI
            _products.Clear();
            foreach (var product in pagedResults)
            {
                _products.Add(product);
            }

            UpdatePaginationUI();
            ResultsCountTextBlock.Text = $"{results.Count} produits trouvés";
        }

        private void UpdatePaginationUI()
        {
            PageInfoTextBlock.Text = $"Page {_currentPage} sur {_totalPages}";
            PreviousPageButton.IsEnabled = _currentPage > 1;
            NextPageButton.IsEnabled = _currentPage < _totalPages;
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            _currentPage = 1;
            ApplyFilters();
        }

        private void CategoryComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _currentPage = 1;
            ApplyFilters();
        }

        private void StatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _currentPage = 1;
            ApplyFilters();
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadProducts();
        }

        private void AddProductButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new ProductDialog(_authService);
            if (dialog.ShowDialog() == true)
            {
                _ = LoadProducts();
            }
        }

        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Product product)
            {
                var dialog = new ProductDialog(_authService, product);
                if (dialog.ShowDialog() == true)
                {
                    _ = LoadProducts();
                }
            }
        }

        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Product product)
            {
                var result = MessageBox.Show(
                    $"Êtes-vous sûr de vouloir supprimer le produit '{product.Name}' ?",
                    "Confirmation de suppression",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await _productRepository.DeleteAsync(product.Id);
                        await LoadProducts();
                        MessageBox.Show("Produit supprimé avec succès.", "Succès",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Erreur lors de la suppression: {ex.Message}",
                                      "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void BarcodeButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Product product)
            {
                var dialog = new BarcodeDialog(product);
                dialog.ShowDialog();
            }
        }

        private void ProductsDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (ProductsDataGrid.SelectedItem is Product product)
            {
                var dialog = new ProductDialog(_authService, product);
                if (dialog.ShowDialog() == true)
                {
                    _ = LoadProducts();
                }
            }
        }

        private void PreviousPageButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage--;
                ApplyFilters();
            }
        }

        private void NextPageButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage < _totalPages)
            {
                _currentPage++;
                ApplyFilters();
            }
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement export functionality
            MessageBox.Show("Fonctionnalité d'export à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement print functionality
            MessageBox.Show("Fonctionnalité d'impression à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
