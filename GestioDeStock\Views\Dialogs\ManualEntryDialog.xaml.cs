using GestioDeStock.Data;
using GestioDeStock.Data.Repositories;
using GestioDeStock.Models;
using GestioDeStock.Services;
using System.Windows;
using System.Windows.Controls;

namespace GestioDeStock.Views.Dialogs
{
    public partial class ManualEntryDialog : Window
    {
        private readonly ProductRepository _productRepository;
        private Product? _selectedProduct;

        public Product? SelectedProduct => _selectedProduct;
        public decimal Quantity { get; private set; }
        public decimal UnitPrice { get; private set; }
        public decimal DiscountPercentage { get; private set; }
        public decimal LineTotal { get; private set; }

        public ManualEntryDialog()
        {
            InitializeComponent();
            
            var config = ConfigurationService.Instance;
            var database = new DatabaseConnection(config.GetConnectionString());
            _productRepository = new ProductRepository(database);
            
            LoadProducts();
        }

        private async void LoadProducts()
        {
            try
            {
                var products = await _productRepository.GetActiveAsync();
                ProductComboBox.ItemsSource = products;
                ProductComboBox.DisplayMemberPath = "Name";
                ProductComboBox.SelectedValuePath = "Id";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des produits: {ex.Message}", 
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ProductComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ProductComboBox.SelectedItem is Product product)
            {
                _selectedProduct = product;
                UnitPriceTextBox.Text = product.PrixVenteTtc.ToString("F2");
                UpdateCalculations();
                AddButton.IsEnabled = true;
            }
            else
            {
                _selectedProduct = null;
                AddButton.IsEnabled = false;
            }
        }

        private void QuantityTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateCalculations();
        }

        private void UnitPriceTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateCalculations();
        }

        private void DiscountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateCalculations();
        }

        private void UpdateCalculations()
        {
            if (_selectedProduct == null) return;

            // Parse values
            if (!decimal.TryParse(QuantityTextBox.Text, out decimal quantity) || quantity <= 0)
            {
                quantity = 1;
                QuantityTextBox.Text = "1";
            }

            if (!decimal.TryParse(UnitPriceTextBox.Text, out decimal unitPrice) || unitPrice < 0)
            {
                unitPrice = _selectedProduct.PrixVenteTtc;
                UnitPriceTextBox.Text = unitPrice.ToString("F2");
            }

            if (!decimal.TryParse(DiscountTextBox.Text, out decimal discountPercentage) || discountPercentage < 0)
            {
                discountPercentage = 0;
                DiscountTextBox.Text = "0";
            }

            // Ensure discount doesn't exceed 100%
            if (discountPercentage > 100)
            {
                discountPercentage = 100;
                DiscountTextBox.Text = "100";
            }

            // Calculate totals
            var subtotal = quantity * unitPrice;
            var discountAmount = subtotal * (discountPercentage / 100);
            var afterDiscount = subtotal - discountAmount;
            
            // Calculate TVA (assuming price includes TVA)
            var tvaRate = _selectedProduct.TvaRate;
            var priceHT = afterDiscount / (1 + tvaRate / 100);
            var tvaAmount = afterDiscount - priceHT;

            // Update UI
            SubtotalTextBlock.Text = $"{subtotal:N2} DA";
            DiscountAmountTextBlock.Text = $"{discountAmount:N2} DA";
            TvaTextBlock.Text = $"{tvaAmount:N2} DA";
            TotalTextBlock.Text = $"{afterDiscount:N2} DA";

            // Store values
            Quantity = quantity;
            UnitPrice = unitPrice;
            DiscountPercentage = discountPercentage;
            LineTotal = afterDiscount;
        }

        private void AddButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedProduct == null)
            {
                MessageBox.Show("Veuillez sélectionner un produit.", "Validation", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (Quantity <= 0)
            {
                MessageBox.Show("La quantité doit être supérieure à zéro.", "Validation", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (UnitPrice < 0)
            {
                MessageBox.Show("Le prix unitaire ne peut pas être négatif.", "Validation", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
