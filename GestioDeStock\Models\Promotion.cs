using System;
using System.Collections.Generic;

namespace GestioDeStock.Models
{
    public class Promotion : BaseEntity
    {
        public string Name { get; set; } = string.Empty;
        public PromotionType Type { get; set; }
        public string? Description { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public DiscountType? DiscountType { get; set; }
        public decimal? DiscountValue { get; set; }
        public decimal? MinQuantity { get; set; }
        public decimal? MaxQuantity { get; set; }
        public bool IsActive { get; set; } = true;

        // Navigation properties
        public List<PromotionProduct> PromotionProducts { get; set; } = new List<PromotionProduct>();
    }

    public enum PromotionType
    {
        Remise,
        Pack,
        OffreSpéciale
    }

    public enum DiscountType
    {
        Pourcentage,
        MontantFixe
    }
}
