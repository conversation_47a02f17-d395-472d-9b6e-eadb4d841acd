using Dapper;
using GestioDeStock.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace GestioDeStock.Data.Repositories
{
    public class BrandRepository : BaseRepository<Brand>
    {
        public BrandRepository(DatabaseConnection database) : base(database, "brands")
        {
        }

        public override async Task<int> AddAsync(Brand entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                INSERT INTO brands (name, description, logo, is_active, created_by, updated_by)
                VALUES (@Name, @Description, @Logo, @IsActive, @CreatedBy, @UpdatedBy);
                SELECT LAST_INSERT_ID();";
            
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Brand entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE brands SET 
                    name = @Name,
                    description = @Description,
                    logo = @Logo,
                    is_active = @IsActive,
                    updated_at = NOW(),
                    updated_by = @UpdatedBy
                WHERE id = @Id";
            
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public override async Task<IEnumerable<Brand>> SearchAsync(string searchTerm)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT * FROM brands 
                WHERE name LIKE @SearchTerm 
                   OR description LIKE @SearchTerm
                ORDER BY name";
            
            return await connection.QueryAsync<Brand>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<IEnumerable<Brand>> GetActiveBrandsAsync()
        {
            return await GetActiveAsync();
        }

        public async Task<Brand?> GetByNameAsync(string name)
        {
            using var connection = _database.CreateConnection();
            var sql = "SELECT * FROM brands WHERE name = @Name";
            return await connection.QueryFirstOrDefaultAsync<Brand>(sql, new { Name = name });
        }
    }
}
