using GestioDeStock.Services;
using System.Windows;
using System.Windows.Controls;

namespace GestioDeStock.Views.Pages
{
    public partial class DashboardPage : Page
    {
        private readonly AuthenticationService _authService;

        public DashboardPage(AuthenticationService authService)
        {
            InitializeComponent();
            _authService = authService;
            
            LoadDashboardData();
        }

        private async void LoadDashboardData()
        {
            try
            {
                // Afficher le message de bienvenue
                if (_authService.CurrentUser != null)
                {
                    WelcomeTextBlock.Text = $"Bienvenue, {_authService.CurrentUser.FullName}! " +
                                          $"Aujourd'hui: {DateTime.Now:dddd, dd MMMM yyyy}";
                }

                // Charger les statistiques
                await LoadStatistics();
                
                // Charger les données récentes
                await LoadRecentData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement du tableau de bord: {ex.Message}", 
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadStatistics()
        {
            // TODO: Implémenter le chargement des statistiques depuis la base de données
            
            // Ventes du jour
            TodaySalesAmountTextBlock.Text = "0 DA"; // await GetTodaySalesAmount()
            
            // Total produits
            TotalProductsTextBlock.Text = "0"; // await GetTotalProducts()
            
            // Stock faible
            LowStockTextBlock.Text = "0"; // await GetLowStockCount()
            
            // Total clients
            TotalClientsTextBlock.Text = "0"; // await GetTotalClients()
        }

        private async Task LoadRecentData()
        {
            // TODO: Charger les ventes récentes et produits en stock faible
            
            // Ventes récentes
            RecentSalesDataGrid.ItemsSource = new List<object>(); // await GetRecentSales()
            
            // Produits en stock faible
            LowStockDataGrid.ItemsSource = new List<object>(); // await GetLowStockProducts()
        }

        private void NewSaleButton_Click(object sender, RoutedEventArgs e)
        {
            // Naviguer vers la page de vente
            if (_authService.CanAccessSales())
            {
                NavigationService?.Navigate(new SalesPage(_authService));
            }
        }

        private void AddProductButton_Click(object sender, RoutedEventArgs e)
        {
            // Naviguer vers la page d'ajout de produit
            if (_authService.CanManageProducts())
            {
                NavigationService?.Navigate(new ProductsPage(_authService));
            }
        }

        private void NewPurchaseButton_Click(object sender, RoutedEventArgs e)
        {
            // Naviguer vers la page d'achat
            if (_authService.CanAccessPurchases())
            {
                NavigationService?.Navigate(new PurchasesPage(_authService));
            }
        }

        private void StockReportButton_Click(object sender, RoutedEventArgs e)
        {
            // Naviguer vers les rapports de stock
            NavigationService?.Navigate(new StockPage(_authService));
        }

        private void AddClientButton_Click(object sender, RoutedEventArgs e)
        {
            // Naviguer vers la page d'ajout de client
            NavigationService?.Navigate(new ClientsPage(_authService));
        }

        private void CashSessionButton_Click(object sender, RoutedEventArgs e)
        {
            // Naviguer vers la gestion de caisse
            NavigationService?.Navigate(new CashPage(_authService));
        }

        private void BackupButton_Click(object sender, RoutedEventArgs e)
        {
            // Effectuer une sauvegarde
            MessageBox.Show("Fonctionnalité de sauvegarde à implémenter", 
                          "Information", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            // Naviguer vers les paramètres
            if (_authService.CanAccessSettings())
            {
                NavigationService?.Navigate(new SettingsPage(_authService));
            }
        }
    }
}
