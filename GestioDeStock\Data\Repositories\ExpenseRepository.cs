using Dapper;
using GestioDeStock.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace GestioDeStock.Data.Repositories
{
    public class ExpenseRepository : BaseRepository<Expense>
    {
        public ExpenseRepository(DatabaseConnection database) : base(database, "expenses")
        {
        }

        public override async Task<int> AddAsync(Expense entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                INSERT INTO expenses (reference_number, category_id, type, description, amount, 
                    payment_method, bank_account_id, check_number, check_date, transaction_date, 
                    receipt_image, notes, created_by, updated_by)
                VALUES (@ReferenceNumber, @CategoryId, @Type, @Description, @Amount, @PaymentMethod, 
                    @BankAccountId, @CheckNumber, @CheckDate, @TransactionDate, @ReceiptImage, 
                    @Notes, @CreatedBy, @UpdatedBy);
                SELECT LAST_INSERT_ID();";
            
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Expense entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE expenses SET 
                    reference_number = @ReferenceNumber,
                    category_id = @CategoryId,
                    type = @Type,
                    description = @Description,
                    amount = @Amount,
                    payment_method = @PaymentMethod,
                    bank_account_id = @BankAccountId,
                    check_number = @CheckNumber,
                    check_date = @CheckDate,
                    transaction_date = @TransactionDate,
                    receipt_image = @ReceiptImage,
                    notes = @Notes,
                    updated_at = NOW(),
                    updated_by = @UpdatedBy
                WHERE id = @Id";
            
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public override async Task<IEnumerable<Expense>> SearchAsync(string searchTerm)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT e.*, ec.name as CategoryName
                FROM expenses e
                LEFT JOIN expense_categories ec ON e.category_id = ec.id
                WHERE e.reference_number LIKE @SearchTerm 
                   OR e.description LIKE @SearchTerm
                   OR ec.name LIKE @SearchTerm
                ORDER BY e.transaction_date DESC";
            
            return await connection.QueryAsync<Expense>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<string> GetNextReferenceNumberAsync(ExpenseType type)
        {
            using var connection = _database.CreateConnection();
            var prefix = type == ExpenseType.Dépense ? "DEP" : "REC";
            var sql = @"
                SELECT CONCAT(@Prefix, LPAD(COALESCE(MAX(CAST(SUBSTRING(reference_number, 4) AS UNSIGNED)), 0) + 1, 6, '0'))
                FROM expenses 
                WHERE reference_number LIKE CONCAT(@Prefix, '%')";
            
            return await connection.QuerySingleAsync<string>(sql, new { Prefix = prefix });
        }

        public async Task<IEnumerable<Expense>> GetExpensesByDateRangeAsync(DateTime fromDate, DateTime toDate, ExpenseType? type = null)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT e.*, ec.name as CategoryName
                FROM expenses e
                LEFT JOIN expense_categories ec ON e.category_id = ec.id
                WHERE e.transaction_date BETWEEN @FromDate AND @ToDate";
            
            if (type.HasValue)
            {
                sql += " AND e.type = @Type";
            }
            
            sql += " ORDER BY e.transaction_date DESC";
            
            return await connection.QueryAsync<Expense>(sql, new { FromDate = fromDate, ToDate = toDate, Type = type?.ToString() });
        }

        public async Task<decimal> GetTotalExpensesAsync(DateTime fromDate, DateTime toDate, ExpenseType? type = null)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT COALESCE(SUM(amount), 0) 
                FROM expenses 
                WHERE transaction_date BETWEEN @FromDate AND @ToDate";
            
            if (type.HasValue)
            {
                sql += " AND type = @Type";
            }
            
            return await connection.QuerySingleAsync<decimal>(sql, new { FromDate = fromDate, ToDate = toDate, Type = type?.ToString() });
        }

        public async Task<IEnumerable<Expense>> GetTodaysExpensesAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT e.*, ec.name as CategoryName
                FROM expenses e
                LEFT JOIN expense_categories ec ON e.category_id = ec.id
                WHERE DATE(e.transaction_date) = CURDATE()
                ORDER BY e.transaction_date DESC";
            
            return await connection.QueryAsync<Expense>(sql);
        }

        public async Task<IEnumerable<dynamic>> GetExpensesByCategoryAsync(DateTime fromDate, DateTime toDate)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT 
                    ec.name as CategoryName,
                    e.type as ExpenseType,
                    COUNT(*) as TransactionCount,
                    SUM(e.amount) as TotalAmount
                FROM expenses e
                INNER JOIN expense_categories ec ON e.category_id = ec.id
                WHERE e.transaction_date BETWEEN @FromDate AND @ToDate
                GROUP BY ec.id, ec.name, e.type
                ORDER BY TotalAmount DESC";
            
            return await connection.QueryAsync(sql, new { FromDate = fromDate, ToDate = toDate });
        }

        public async Task<IEnumerable<dynamic>> GetMonthlyExpenseSummaryAsync(int year)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT 
                    MONTH(transaction_date) as Month,
                    MONTHNAME(transaction_date) as MonthName,
                    type as ExpenseType,
                    SUM(amount) as TotalAmount,
                    COUNT(*) as TransactionCount
                FROM expenses
                WHERE YEAR(transaction_date) = @Year
                GROUP BY MONTH(transaction_date), MONTHNAME(transaction_date), type
                ORDER BY Month, type";
            
            return await connection.QueryAsync(sql, new { Year = year });
        }

        
    }
}
