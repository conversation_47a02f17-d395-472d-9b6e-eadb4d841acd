<Window x:Class="GestioDeStock.Views.Dialogs.CloseCashDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Fermeture de Caisse" 
        Height="500" Width="550"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <materialDesign:PackIcon Kind="CashLock" Width="48" Height="48" 
                                   Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                   HorizontalAlignment="Center" Margin="0,0,0,10"/>
            <TextBlock Text="Fermeture de Session de Caisse" FontSize="18" FontWeight="Bold" 
                     HorizontalAlignment="Center"/>
            <TextBlock x:Name="SessionInfoTextBlock" Text="Session: CS20240101001" FontSize="14" 
                     HorizontalAlignment="Center" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
        </StackPanel>
        
        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- Session Summary -->
                <materialDesign:Card Padding="15">
                    <StackPanel>
                        <TextBlock Text="Résumé de la Session" FontSize="16" FontWeight="Medium" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="Ouverture:" FontSize="12"/>
                            <TextBlock x:Name="OpeningTimeTextBlock" Grid.Row="0" Grid.Column="1" Text="" FontSize="12" FontWeight="Bold"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Montant d'ouverture:" FontSize="12"/>
                            <TextBlock x:Name="OpeningAmountTextBlock" Grid.Row="1" Grid.Column="1" Text="0.00 DA" FontSize="12" FontWeight="Bold"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="Total ventes:" FontSize="12"/>
                            <TextBlock x:Name="TotalSalesTextBlock" Grid.Row="2" Grid.Column="1" Text="0.00 DA" FontSize="12" FontWeight="Bold" Foreground="#4CAF50"/>
                            
                            <TextBlock Grid.Row="3" Grid.Column="0" Text="Espèces:" FontSize="12"/>
                            <TextBlock x:Name="CashSalesTextBlock" Grid.Row="3" Grid.Column="1" Text="0.00 DA" FontSize="12" FontWeight="Bold"/>
                            
                            <TextBlock Grid.Row="4" Grid.Column="0" Text="Cartes:" FontSize="12"/>
                            <TextBlock x:Name="CardSalesTextBlock" Grid.Row="4" Grid.Column="1" Text="0.00 DA" FontSize="12" FontWeight="Bold"/>
                            
                            <Separator Grid.Row="5" Grid.ColumnSpan="2" Margin="0,10"/>
                            
                            <TextBlock Grid.Row="6" Grid.Column="0" Text="Espèces attendues:" FontSize="14" FontWeight="Medium"/>
                            <TextBlock x:Name="ExpectedCashTextBlock" Grid.Row="6" Grid.Column="1" Text="0.00 DA" FontSize="14" FontWeight="Bold" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>
                
                <!-- Cash Count -->
                <materialDesign:Card Padding="15">
                    <StackPanel>
                        <TextBlock Text="Comptage des Espèces" FontSize="16" FontWeight="Medium" Margin="0,0,0,15"/>
                        
                        <TextBox x:Name="ActualCashTextBox"
                               materialDesign:HintAssist.Hint="Montant réel compté (DA)"
                               Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                               FontSize="16"
                               TextChanged="ActualCashTextBox_TextChanged"/>
                        
                        <StackPanel Margin="0,15,0,0">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" Text="Différence:" FontSize="14" FontWeight="Medium"/>
                                <TextBlock x:Name="DifferenceTextBlock" Grid.Column="1" Text="0.00 DA" FontSize="14" FontWeight="Bold"/>
                            </Grid>
                            
                            <TextBlock x:Name="DifferenceStatusTextBlock" Text="" FontSize="12" 
                                     HorizontalAlignment="Right" Margin="0,5,0,0"/>
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>
                
                <!-- Notes -->
                <TextBox x:Name="NotesTextBox"
                       materialDesign:HintAssist.Hint="Notes de fermeture (optionnel)"
                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                       AcceptsReturn="True"
                       TextWrapping="Wrap"
                       MinLines="3"
                       MaxLines="4"/>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="CloseButton" 
                  Style="{StaticResource MaterialDesignRaisedButton}"
                  Content="FERMER CAISSE" 
                  Margin="0,0,10,0"
                  Click="CloseButton_Click">
                <Button.ContentTemplate>
                    <DataTemplate>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="CashLock" Width="16" Height="16" 
                                                   VerticalAlignment="Center" Margin="0,0,5,0"/>
                            <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                        </StackPanel>
                    </DataTemplate>
                </Button.ContentTemplate>
            </Button>
            
            <Button x:Name="CancelButton" 
                  Style="{StaticResource MaterialDesignOutlinedButton}"
                  Content="ANNULER" 
                  Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
