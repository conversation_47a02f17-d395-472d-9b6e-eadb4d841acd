using GestioDeStock.Models;
using System.Windows;
using System.Windows.Controls;

namespace GestioDeStock.Views.Dialogs
{
    public partial class CloseCashDialog : Window
    {
        private readonly CashSession _session;
        private decimal _expectedCash;

        public decimal ClosingAmount { get; private set; }
        public string Notes { get; private set; } = string.Empty;
        public decimal Difference { get; private set; }

        public CloseCashDialog(CashSession session)
        {
            InitializeComponent();
            _session = session;
            
            LoadSessionData();
        }

        private void LoadSessionData()
        {
            try
            {
                // Display session info
                SessionInfoTextBlock.Text = $"Session: {_session.SessionNumber} - {_session.OpenedAt:dd/MM/yyyy}";
                OpeningTimeTextBlock.Text = _session.OpenedAt.ToString("dd/MM/yyyy HH:mm");
                OpeningAmountTextBlock.Text = $"{_session.OpeningAmount:N2} DA";
                TotalSalesTextBlock.Text = $"{_session.TotalSales:N2} DA";
                CashSalesTextBlock.Text = $"{_session.TotalCash:N2} DA";
                CardSalesTextBlock.Text = $"{_session.TotalCard:N2} DA";
                
                // Calculate expected cash
                _expectedCash = _session.OpeningAmount + _session.TotalCash;
                ExpectedCashTextBlock.Text = $"{_expectedCash:N2} DA";
                
                // Set focus
                ActualCashTextBox.Focus();
                ActualCashTextBox.Text = _expectedCash.ToString("F2");
                ActualCashTextBox.SelectAll();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des données: {ex.Message}", 
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ActualCashTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (decimal.TryParse(ActualCashTextBox.Text, out decimal actualAmount))
            {
                Difference = actualAmount - _expectedCash;
                DifferenceTextBlock.Text = $"{Math.Abs(Difference):N2} DA";
                
                if (Difference == 0)
                {
                    DifferenceTextBlock.Foreground = System.Windows.Media.Brushes.Green;
                    DifferenceStatusTextBlock.Text = "✓ Caisse équilibrée";
                    DifferenceStatusTextBlock.Foreground = System.Windows.Media.Brushes.Green;
                }
                else if (Difference > 0)
                {
                    DifferenceTextBlock.Foreground = System.Windows.Media.Brushes.Blue;
                    DifferenceStatusTextBlock.Text = $"+ Excédent de {Math.Abs(Difference):N2} DA";
                    DifferenceStatusTextBlock.Foreground = System.Windows.Media.Brushes.Blue;
                }
                else
                {
                    DifferenceTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                    DifferenceStatusTextBlock.Text = $"- Manque de {Math.Abs(Difference):N2} DA";
                    DifferenceStatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                }
                
                ClosingAmount = actualAmount;
            }
            else
            {
                DifferenceTextBlock.Text = "0.00 DA";
                DifferenceTextBlock.Foreground = System.Windows.Media.Brushes.Black;
                DifferenceStatusTextBlock.Text = "";
                ClosingAmount = 0;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validation
                if (!decimal.TryParse(ActualCashTextBox.Text, out decimal actualAmount) || actualAmount < 0)
                {
                    MessageBox.Show("Le montant compté doit être un nombre positif.", "Validation", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    ActualCashTextBox.Focus();
                    return;
                }

                // Warning for large differences
                if (Math.Abs(Difference) > 100) // More than 100 DA difference
                {
                    var result = MessageBox.Show(
                        $"Attention: Il y a une différence importante de {Math.Abs(Difference):N2} DA.\n" +
                        "Voulez-vous continuer la fermeture ?",
                        "Différence importante",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.No)
                    {
                        ActualCashTextBox.Focus();
                        return;
                    }
                }

                // Final confirmation
                var confirmResult = MessageBox.Show(
                    $"Confirmer la fermeture de la caisse ?\n\n" +
                    $"Montant attendu: {_expectedCash:N2} DA\n" +
                    $"Montant compté: {actualAmount:N2} DA\n" +
                    $"Différence: {(Difference >= 0 ? "+" : "")}{Difference:N2} DA",
                    "Confirmation de fermeture",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (confirmResult == MessageBoxResult.Yes)
                {
                    ClosingAmount = actualAmount;
                    Notes = NotesTextBox.Text.Trim();
                    
                    DialogResult = true;
                    Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur: {ex.Message}", 
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
