using GestioDeStock.Services;
using System.Windows;

namespace GestioDeStock;

public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        // Initialize configuration service
        var config = ConfigurationService.Instance;

        // Ensure required directories exist
        config.EnsureDirectoriesExist();

        // Set up global exception handling
        DispatcherUnhandledException += App_DispatcherUnhandledException;
        AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
    }

    private void App_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
    {
        MessageBox.Show($"Une erreur inattendue s'est produite:\n{e.Exception.Message}",
                       "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
        e.Handled = true;
    }

    private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        var exception = e.ExceptionObject as Exception;
        MessageBox.Show($"Erreur critique:\n{exception?.Message}",
                       "Erreur Critique", MessageBoxButton.OK, MessageBoxImage.Error);
    }
}

