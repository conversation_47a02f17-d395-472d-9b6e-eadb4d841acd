<Window x:Class="GestioDeStock.Views.Dialogs.AddClientDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Ajouter Client" 
        Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Nouveau Client" FontSize="20" FontWeight="Bold" Margin="0,0,0,20"/>
        
        <!-- Form -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Spacing="15">
                
                <!-- Basic Information -->
                <TextBlock Text="Informations de Base" FontSize="16" FontWeight="Medium" Margin="0,0,0,10"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox x:Name="CodeTextBox" Grid.Column="0"
                           materialDesign:HintAssist.Hint="Code client (auto)"
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           Margin="0,0,10,0"
                           IsReadOnly="True"/>
                    
                    <TextBox x:Name="NameTextBox" Grid.Column="1"
                           materialDesign:HintAssist.Hint="Nom complet *"
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           Margin="10,0,0,0"/>
                </Grid>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox x:Name="PhoneTextBox" Grid.Column="0"
                           materialDesign:HintAssist.Hint="Téléphone"
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           Margin="0,0,10,0"/>
                    
                    <TextBox x:Name="EmailTextBox" Grid.Column="1"
                           materialDesign:HintAssist.Hint="Email"
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           Margin="10,0,0,0"/>
                </Grid>
                
                <TextBox x:Name="AddressTextBox"
                       materialDesign:HintAssist.Hint="Adresse complète"
                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                       AcceptsReturn="True"
                       TextWrapping="Wrap"
                       MinLines="2"
                       MaxLines="3"/>
                
                <Separator Margin="0,10"/>
                
                <!-- Legal Information -->
                <TextBlock Text="Informations Légales" FontSize="16" FontWeight="Medium" Margin="0,0,0,10"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox x:Name="NifTextBox" Grid.Column="0"
                           materialDesign:HintAssist.Hint="NIF"
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           Margin="0,0,10,0"/>
                    
                    <TextBox x:Name="NisTextBox" Grid.Column="1"
                           materialDesign:HintAssist.Hint="NIS"
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           Margin="10,0,0,0"/>
                </Grid>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox x:Name="RcTextBox" Grid.Column="0"
                           materialDesign:HintAssist.Hint="RC"
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           Margin="0,0,10,0"/>
                    
                    <TextBox x:Name="ArticleTextBox" Grid.Column="1"
                           materialDesign:HintAssist.Hint="Article"
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           Margin="10,0,0,0"/>
                </Grid>
                
                <Separator Margin="0,10"/>
                
                <!-- Financial Information -->
                <TextBlock Text="Informations Financières" FontSize="16" FontWeight="Medium" Margin="0,0,0,10"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox x:Name="CreditLimitTextBox" Grid.Column="0"
                           materialDesign:HintAssist.Hint="Limite de crédit (DA)"
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           Margin="0,0,10,0"
                           Text="0"/>
                    
                    <TextBox x:Name="OpeningBalanceTextBox" Grid.Column="1"
                           materialDesign:HintAssist.Hint="Solde d'ouverture (DA)"
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           Margin="10,0,0,0"
                           Text="0"/>
                </Grid>
                
                <CheckBox x:Name="IsActiveCheckBox"
                        Content="Client actif"
                        Style="{StaticResource MaterialDesignCheckBox}"
                        IsChecked="True"/>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="SaveButton" 
                  Style="{StaticResource MaterialDesignRaisedButton}"
                  Content="ENREGISTRER" 
                  Margin="0,0,10,0"
                  Click="SaveButton_Click"/>
            
            <Button x:Name="CancelButton" 
                  Style="{StaticResource MaterialDesignOutlinedButton}"
                  Content="ANNULER" 
                  Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
