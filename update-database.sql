-- =====================================================
-- Script de Mise à Jour de la Base de Données
-- Version 1.0.1
-- =====================================================

USE `gestion_stock`;

-- Vérifier la version actuelle
CREATE TABLE IF NOT EXISTS `database_version` (
  `version` varchar(10) NOT NULL,
  `applied_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insérer la version initiale si elle n'existe pas
INSERT IGNORE INTO `database_version` (`version`) VALUES ('1.0.0');

-- =====================================================
-- Mises à jour pour la version 1.0.1
-- =====================================================

-- Ajouter des index pour améliorer les performances
ALTER TABLE `sales_invoices` ADD INDEX `idx_invoice_date_status` (`invoice_date`, `status`);
ALTER TABLE `purchase_invoices` ADD INDEX `idx_invoice_date_status` (`invoice_date`, `status`);
ALTER TABLE `stock_movements` ADD INDEX `idx_movement_date_type` (`movement_date`, `movement_type`);

-- Ajouter des contraintes de validation
ALTER TABLE `products` ADD CONSTRAINT `chk_prices_positive` 
CHECK (`prix_achat` >= 0 AND `prix_vente_ht` >= 0 AND `prix_vente_ttc` >= 0);

ALTER TABLE `product_stock` ADD CONSTRAINT `chk_quantities_positive` 
CHECK (`quantity_available` >= 0 AND `quantity_reserved` >= 0);

-- Ajouter des triggers pour maintenir la cohérence
DELIMITER $$

-- Trigger pour mettre à jour le stock lors d'une vente
CREATE TRIGGER `tr_sales_invoice_details_stock_update` 
AFTER INSERT ON `sales_invoice_details`
FOR EACH ROW
BEGIN
    -- Mettre à jour le stock disponible
    UPDATE `product_stock` 
    SET `quantity_available` = `quantity_available` - NEW.quantity,
        `last_updated` = NOW()
    WHERE `product_id` = NEW.product_id 
      AND `warehouse_id` = (SELECT warehouse_id FROM sales_invoices WHERE id = NEW.invoice_id);
    
    -- Insérer un mouvement de stock
    INSERT INTO `stock_movements` (
        `product_id`, `warehouse_id`, `movement_type`, `reference_type`, 
        `reference_id`, `reference_number`, `quantity`, `unit_cost`, 
        `total_cost`, `movement_date`, `created_by`
    )
    SELECT 
        NEW.product_id,
        si.warehouse_id,
        'Sortie',
        'Vente',
        NEW.invoice_id,
        si.invoice_number,
        NEW.quantity,
        NEW.cost_price,
        NEW.quantity * NEW.cost_price,
        NOW(),
        si.created_by
    FROM sales_invoices si 
    WHERE si.id = NEW.invoice_id;
END$$

-- Trigger pour mettre à jour le stock lors d'un achat
CREATE TRIGGER `tr_purchase_invoice_details_stock_update` 
AFTER INSERT ON `purchase_invoice_details`
FOR EACH ROW
BEGIN
    -- Mettre à jour le stock disponible
    INSERT INTO `product_stock` (
        `product_id`, `warehouse_id`, `quantity_available`, 
        `last_cost`, `average_cost`
    )
    SELECT 
        NEW.product_id,
        pi.warehouse_id,
        NEW.quantity,
        NEW.unit_cost_ht,
        NEW.unit_cost_ht
    FROM purchase_invoices pi 
    WHERE pi.id = NEW.invoice_id
    ON DUPLICATE KEY UPDATE
        `quantity_available` = `quantity_available` + NEW.quantity,
        `last_cost` = NEW.unit_cost_ht,
        `average_cost` = ((`average_cost` * `quantity_available`) + (NEW.unit_cost_ht * NEW.quantity)) / (`quantity_available` + NEW.quantity),
        `last_updated` = NOW();
    
    -- Insérer un mouvement de stock
    INSERT INTO `stock_movements` (
        `product_id`, `warehouse_id`, `movement_type`, `reference_type`, 
        `reference_id`, `reference_number`, `quantity`, `unit_cost`, 
        `total_cost`, `movement_date`, `created_by`
    )
    SELECT 
        NEW.product_id,
        pi.warehouse_id,
        'Entrée',
        'Achat',
        NEW.invoice_id,
        pi.invoice_number,
        NEW.quantity,
        NEW.unit_cost_ht,
        NEW.quantity * NEW.unit_cost_ht,
        NOW(),
        pi.created_by
    FROM purchase_invoices pi 
    WHERE pi.id = NEW.invoice_id;
END$$

DELIMITER ;

-- =====================================================
-- Vues utiles pour les rapports
-- =====================================================

-- Vue pour le stock actuel avec informations produit
CREATE OR REPLACE VIEW `v_current_stock` AS
SELECT 
    p.id as product_id,
    p.code,
    p.name as product_name,
    c.name as category_name,
    b.name as brand_name,
    u.name as unit_name,
    w.name as warehouse_name,
    ps.quantity_available,
    ps.quantity_reserved,
    ps.quantity_total,
    ps.last_cost,
    ps.average_cost,
    p.min_stock_alert,
    p.max_stock_alert,
    CASE 
        WHEN ps.quantity_available <= p.min_stock_alert THEN 'Faible'
        WHEN ps.quantity_available >= COALESCE(p.max_stock_alert, 999999) THEN 'Élevé'
        ELSE 'Normal'
    END as stock_status,
    ps.last_updated
FROM products p
LEFT JOIN product_stock ps ON p.id = ps.product_id
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN brands b ON p.brand_id = b.id
LEFT JOIN units u ON p.unit_id = u.id
LEFT JOIN warehouses w ON ps.warehouse_id = w.id
WHERE p.is_active = 1 AND p.is_archived = 0;

-- Vue pour les ventes du jour
CREATE OR REPLACE VIEW `v_daily_sales` AS
SELECT 
    DATE(si.invoice_date) as sale_date,
    COUNT(*) as invoice_count,
    SUM(si.subtotal_ht) as total_ht,
    SUM(si.total_tva) as total_tva,
    SUM(si.total_ttc) as total_ttc,
    SUM(si.discount_amount) as total_discount,
    SUM(si.final_amount) as total_final,
    AVG(si.final_amount) as average_sale
FROM sales_invoices si
WHERE si.status != 'Annulé'
GROUP BY DATE(si.invoice_date)
ORDER BY sale_date DESC;

-- Vue pour les produits les plus vendus
CREATE OR REPLACE VIEW `v_top_selling_products` AS
SELECT 
    p.id as product_id,
    p.code,
    p.name as product_name,
    c.name as category_name,
    SUM(sid.quantity) as total_quantity_sold,
    SUM(sid.line_total_ttc) as total_revenue,
    COUNT(DISTINCT sid.invoice_id) as invoice_count,
    AVG(sid.unit_price_ttc) as average_price
FROM products p
INNER JOIN sales_invoice_details sid ON p.id = sid.product_id
INNER JOIN sales_invoices si ON sid.invoice_id = si.id
LEFT JOIN categories c ON p.category_id = c.id
WHERE si.status != 'Annulé'
  AND si.invoice_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY p.id, p.code, p.name, c.name
ORDER BY total_quantity_sold DESC;

-- Marquer cette version comme appliquée
INSERT INTO `database_version` (`version`) VALUES ('1.0.1')
ON DUPLICATE KEY UPDATE `applied_at` = CURRENT_TIMESTAMP;

-- =====================================================
-- Données de test (optionnel)
-- =====================================================

-- Insérer quelques produits de test
INSERT IGNORE INTO `products` (
    `code`, `name`, `category_id`, `brand_id`, `unit_id`, 
    `prix_achat`, `prix_vente_ht`, `prix_vente_ttc`, `tva_rate`,
    `min_stock_alert`, `is_active`, `created_by`, `updated_by`
) VALUES 
('PROD001', 'Lait 1L', 1, 1, 1, 80.00, 95.00, 113.05, 19.00, 10, 1, 1, 1),
('PROD002', 'Pain Blanc', 1, 1, 8, 25.00, 30.00, 35.70, 19.00, 20, 1, 1, 1),
('PROD003', 'Huile Tournesol 1L', 1, 2, 1, 180.00, 220.00, 261.80, 19.00, 5, 1, 1, 1),
('PROD004', 'Sucre Blanc 1Kg', 1, 2, 2, 120.00, 150.00, 178.50, 19.00, 15, 1, 1, 1),
('PROD005', 'Café Moulu 250g', 1, 3, 3, 300.00, 380.00, 452.20, 19.00, 8, 1, 1, 1);

-- Insérer du stock initial
INSERT IGNORE INTO `product_stock` (
    `product_id`, `warehouse_id`, `quantity_available`, 
    `last_cost`, `average_cost`
) VALUES 
(1, 1, 50, 80.00, 80.00),
(2, 1, 100, 25.00, 25.00),
(3, 1, 30, 180.00, 180.00),
(4, 1, 75, 120.00, 120.00),
(5, 1, 25, 300.00, 300.00);

SELECT 'Mise à jour de la base de données terminée avec succès!' as message;
