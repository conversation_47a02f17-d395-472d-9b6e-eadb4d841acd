<Page x:Class="GestioDeStock.Views.Pages.ReportsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="Rapports et Statistiques">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Style="{StaticResource CardStyle}">
            <DockPanel>
                <StackPanel DockPanel.Dock="Left">
                    <TextBlock Text="Rapports et Statistiques" FontSize="24" FontWeight="Bold"/>
                    <TextBlock Text="Générer et consulter les rapports d'activité" FontSize="14"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>

                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal" VerticalAlignment="Center">
                    <DatePicker x:Name="FromDatePicker"
                              materialDesign:HintAssist.Hint="Du"
                              Style="{StaticResource MaterialDesignFloatingHintDatePicker}"
                              Margin="5,0"
                              SelectedDateChanged="DatePicker_SelectedDateChanged"/>

                    <DatePicker x:Name="ToDatePicker"
                              materialDesign:HintAssist.Hint="Au"
                              Style="{StaticResource MaterialDesignFloatingHintDatePicker}"
                              Margin="5,0"
                              SelectedDateChanged="DatePicker_SelectedDateChanged"/>

                    <Button x:Name="RefreshButton"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Content="ACTUALISER"
                          Margin="10,0,0,0"
                          Click="RefreshButton_Click">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Refresh" Width="16" Height="16"
                                                           VerticalAlignment="Center" Margin="0,0,5,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>
                </StackPanel>
            </DockPanel>
        </materialDesign:Card>

        <!-- Reports Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>

                <!-- Sales Reports -->
                <materialDesign:Card Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="Rapports de Ventes" FontSize="18" FontWeight="Medium" Margin="0,0,0,15"/>

                        <UniformGrid Columns="3" Margin="0,0,0,15">
                            <materialDesign:Card Margin="5" Padding="15">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="CashMultiple" Width="32" Height="32"
                                                           Foreground="{DynamicResource PrimaryHueMidBrush}" Margin="0,0,0,10"/>
                                    <TextBlock Text="Chiffre d'Affaires" FontSize="12" HorizontalAlignment="Center"/>
                                    <TextBlock x:Name="TotalSalesAmountTextBlock" Text="0.00 DA" FontSize="16" FontWeight="Bold"
                                             HorizontalAlignment="Center" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <materialDesign:Card Margin="5" Padding="15">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Receipt" Width="32" Height="32"
                                                           Foreground="#4CAF50" Margin="0,0,0,10"/>
                                    <TextBlock Text="Nombre de Ventes" FontSize="12" HorizontalAlignment="Center"/>
                                    <TextBlock x:Name="TotalSalesCountTextBlock" Text="0" FontSize="16" FontWeight="Bold"
                                             HorizontalAlignment="Center" Foreground="#4CAF50"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <materialDesign:Card Margin="5" Padding="15">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="TrendingUp" Width="32" Height="32"
                                                           Foreground="#FF9800" Margin="0,0,0,10"/>
                                    <TextBlock Text="Vente Moyenne" FontSize="12" HorizontalAlignment="Center"/>
                                    <TextBlock x:Name="AverageSaleTextBlock" Text="0.00 DA" FontSize="16" FontWeight="Bold"
                                             HorizontalAlignment="Center" Foreground="#FF9800"/>
                                </StackPanel>
                            </materialDesign:Card>
                        </UniformGrid>

                        <UniformGrid Columns="2">
                            <Button x:Name="SalesReportButton"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Content="RAPPORT DÉTAILLÉ DES VENTES"
                                  Margin="5"
                                  Click="SalesReportButton_Click"/>

                            <Button x:Name="SalesByProductButton"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Content="VENTES PAR PRODUIT"
                                  Margin="5"
                                  Click="SalesByProductButton_Click"/>
                        </UniformGrid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Purchase Reports -->
                <materialDesign:Card Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="Rapports d'Achats" FontSize="18" FontWeight="Medium" Margin="0,0,0,15"/>

                        <UniformGrid Columns="3" Margin="0,0,0,15">
                            <materialDesign:Card Margin="5" Padding="15">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="TruckDelivery" Width="32" Height="32"
                                                           Foreground="#2196F3" Margin="0,0,0,10"/>
                                    <TextBlock Text="Total Achats" FontSize="12" HorizontalAlignment="Center"/>
                                    <TextBlock x:Name="TotalPurchasesAmountTextBlock" Text="0.00 DA" FontSize="16" FontWeight="Bold"
                                             HorizontalAlignment="Center" Foreground="#2196F3"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <materialDesign:Card Margin="5" Padding="15">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="FileDocument" Width="32" Height="32"
                                                           Foreground="#9C27B0" Margin="0,0,0,10"/>
                                    <TextBlock Text="Nombre d'Achats" FontSize="12" HorizontalAlignment="Center"/>
                                    <TextBlock x:Name="TotalPurchasesCountTextBlock" Text="0" FontSize="16" FontWeight="Bold"
                                             HorizontalAlignment="Center" Foreground="#9C27B0"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <materialDesign:Card Margin="5" Padding="15">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Truck" Width="32" Height="32"
                                                           Foreground="#607D8B" Margin="0,0,0,10"/>
                                    <TextBlock Text="Fournisseurs Actifs" FontSize="12" HorizontalAlignment="Center"/>
                                    <TextBlock x:Name="ActiveSuppliersTextBlock" Text="0" FontSize="16" FontWeight="Bold"
                                             HorizontalAlignment="Center" Foreground="#607D8B"/>
                                </StackPanel>
                            </materialDesign:Card>
                        </UniformGrid>

                        <UniformGrid Columns="2">
                            <Button x:Name="PurchasesReportButton"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Content="RAPPORT DÉTAILLÉ DES ACHATS"
                                  Margin="5"
                                  Click="PurchasesReportButton_Click"/>

                            <Button x:Name="PurchasesBySupplierButton"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Content="ACHATS PAR FOURNISSEUR"
                                  Margin="5"
                                  Click="PurchasesBySupplierButton_Click"/>
                        </UniformGrid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Stock Reports -->
                <materialDesign:Card Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="Rapports de Stock" FontSize="18" FontWeight="Medium" Margin="0,0,0,15"/>

                        <UniformGrid Columns="3" Margin="0,0,0,15">
                            <materialDesign:Card Margin="5" Padding="15">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Package" Width="32" Height="32"
                                                           Foreground="#795548" Margin="0,0,0,10"/>
                                    <TextBlock Text="Valeur du Stock" FontSize="12" HorizontalAlignment="Center"/>
                                    <TextBlock x:Name="StockValueTextBlock" Text="0.00 DA" FontSize="16" FontWeight="Bold"
                                             HorizontalAlignment="Center" Foreground="#795548"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <materialDesign:Card Margin="5" Padding="15">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="AlertCircle" Width="32" Height="32"
                                                           Foreground="#FF5722" Margin="0,0,0,10"/>
                                    <TextBlock Text="Stock Faible" FontSize="12" HorizontalAlignment="Center"/>
                                    <TextBlock x:Name="LowStockCountTextBlock" Text="0" FontSize="16" FontWeight="Bold"
                                             HorizontalAlignment="Center" Foreground="#FF5722"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <materialDesign:Card Margin="5" Padding="15">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="PackageVariantClosed" Width="32" Height="32"
                                                           Foreground="#3F51B5" Margin="0,0,0,10"/>
                                    <TextBlock Text="Total Produits" FontSize="12" HorizontalAlignment="Center"/>
                                    <TextBlock x:Name="TotalProductsTextBlock" Text="0" FontSize="16" FontWeight="Bold"
                                             HorizontalAlignment="Center" Foreground="#3F51B5"/>
                                </StackPanel>
                            </materialDesign:Card>
                        </UniformGrid>

                        <UniformGrid Columns="3">
                            <Button x:Name="StockReportButton"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Content="ÉTAT DU STOCK"
                                  Margin="5"
                                  Click="StockReportButton_Click"/>

                            <Button x:Name="StockMovementsButton"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Content="MOUVEMENTS DE STOCK"
                                  Margin="5"
                                  Click="StockMovementsButton_Click"/>

                            <Button x:Name="InventoryReportButton"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Content="RAPPORT D'INVENTAIRE"
                                  Margin="5"
                                  Click="InventoryReportButton_Click"/>
                        </UniformGrid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Financial Reports -->
                <materialDesign:Card Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="Rapports Financiers" FontSize="18" FontWeight="Medium" Margin="0,0,0,15"/>

                        <UniformGrid Columns="3" Margin="0,0,0,15">
                            <materialDesign:Card Margin="5" Padding="15">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="CashRegister" Width="32" Height="32"
                                                           Foreground="#4CAF50" Margin="0,0,0,10"/>
                                    <TextBlock Text="Recettes" FontSize="12" HorizontalAlignment="Center"/>
                                    <TextBlock x:Name="TotalIncomeTextBlock" Text="0.00 DA" FontSize="16" FontWeight="Bold"
                                             HorizontalAlignment="Center" Foreground="#4CAF50"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <materialDesign:Card Margin="5" Padding="15">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="CashMinus" Width="32" Height="32"
                                                           Foreground="#F44336" Margin="0,0,0,10"/>
                                    <TextBlock Text="Dépenses" FontSize="12" HorizontalAlignment="Center"/>
                                    <TextBlock x:Name="TotalExpensesTextBlock" Text="0.00 DA" FontSize="16" FontWeight="Bold"
                                             HorizontalAlignment="Center" Foreground="#F44336"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <materialDesign:Card Margin="5" Padding="15">
                                <StackPanel HorizontalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Calculator" Width="32" Height="32"
                                                           Foreground="#FF9800" Margin="0,0,0,10"/>
                                    <TextBlock Text="Bénéfice Net" FontSize="12" HorizontalAlignment="Center"/>
                                    <TextBlock x:Name="NetProfitTextBlock" Text="0.00 DA" FontSize="16" FontWeight="Bold"
                                             HorizontalAlignment="Center" Foreground="#FF9800"/>
                                </StackPanel>
                            </materialDesign:Card>
                        </UniformGrid>

                        <UniformGrid Columns="2">
                            <Button x:Name="FinancialReportButton"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Content="RAPPORT FINANCIER"
                                  Margin="5"
                                  Click="FinancialReportButton_Click"/>

                            <Button x:Name="CashFlowButton"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Content="FLUX DE TRÉSORERIE"
                                  Margin="5"
                                  Click="CashFlowButton_Click"/>
                        </UniformGrid>
                    </StackPanel>
                </materialDesign:Card>

            </StackPanel>
        </ScrollViewer>
    </Grid>
</Page>
