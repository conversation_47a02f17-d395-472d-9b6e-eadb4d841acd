﻿#pragma checksum "..\..\..\..\..\Views\Pages\ReportsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "2E3E8AE1B5107D51E8D00C0444BA6526AA9A4751"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GestioDeStock.Views.Pages {
    
    
    /// <summary>
    /// ReportsPage
    /// </summary>
    public partial class ReportsPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 23 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker FromDatePicker;
        
        #line default
        #line hidden
        
        
        #line 29 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker ToDatePicker;
        
        #line default
        #line hidden
        
        
        #line 35 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSalesAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalSalesCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AverageSaleTextBlock;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SalesReportButton;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SalesByProductButton;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalPurchasesAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalPurchasesCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveSuppliersTextBlock;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PurchasesReportButton;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PurchasesBySupplierButton;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StockValueTextBlock;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LowStockCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalProductsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StockReportButton;
        
        #line default
        #line hidden
        
        
        #line 208 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StockMovementsButton;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InventoryReportButton;
        
        #line default
        #line hidden
        
        
        #line 234 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalIncomeTextBlock;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalExpensesTextBlock;
        
        #line default
        #line hidden
        
        
        #line 254 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NetProfitTextBlock;
        
        #line default
        #line hidden
        
        
        #line 261 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FinancialReportButton;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CashFlowButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GestioDeStock;V1.0.0.0;component/views/pages/reportspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.FromDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 27 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
            this.FromDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DatePicker_SelectedDateChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ToDatePicker = ((System.Windows.Controls.DatePicker)(target));
            
            #line 33 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
            this.ToDatePicker.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DatePicker_SelectedDateChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 39 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.TotalSalesAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.TotalSalesCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.AverageSaleTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.SalesReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 100 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
            this.SalesReportButton.Click += new System.Windows.RoutedEventHandler(this.SalesReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.SalesByProductButton = ((System.Windows.Controls.Button)(target));
            
            #line 106 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
            this.SalesByProductButton.Click += new System.Windows.RoutedEventHandler(this.SalesByProductButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.TotalPurchasesAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.TotalPurchasesCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.ActiveSuppliersTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.PurchasesReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 153 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
            this.PurchasesReportButton.Click += new System.Windows.RoutedEventHandler(this.PurchasesReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.PurchasesBySupplierButton = ((System.Windows.Controls.Button)(target));
            
            #line 159 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
            this.PurchasesBySupplierButton.Click += new System.Windows.RoutedEventHandler(this.PurchasesBySupplierButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.StockValueTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.LowStockCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.TotalProductsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.StockReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 206 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
            this.StockReportButton.Click += new System.Windows.RoutedEventHandler(this.StockReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.StockMovementsButton = ((System.Windows.Controls.Button)(target));
            
            #line 212 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
            this.StockMovementsButton.Click += new System.Windows.RoutedEventHandler(this.StockMovementsButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.InventoryReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 218 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
            this.InventoryReportButton.Click += new System.Windows.RoutedEventHandler(this.InventoryReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.TotalIncomeTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.TotalExpensesTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.NetProfitTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.FinancialReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 265 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
            this.FinancialReportButton.Click += new System.Windows.RoutedEventHandler(this.FinancialReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.CashFlowButton = ((System.Windows.Controls.Button)(target));
            
            #line 271 "..\..\..\..\..\Views\Pages\ReportsPage.xaml"
            this.CashFlowButton.Click += new System.Windows.RoutedEventHandler(this.CashFlowButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

