using GestioDeStock.Data;
using GestioDeStock.Data.Repositories;
using GestioDeStock.Models;
using GestioDeStock.Services;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace GestioDeStock.Views.Dialogs
{
    public partial class ProductSearchDialog : Window
    {
        private readonly ProductRepository _productRepository;
        private readonly CategoryRepository _categoryRepository;
        private ObservableCollection<Product> _products;
        private List<Product> _allProducts;

        public Product? SelectedProduct { get; private set; }

        public ProductSearchDialog()
        {
            InitializeComponent();
            
            var config = ConfigurationService.Instance;
            var database = new DatabaseConnection(config.GetConnectionString());
            _productRepository = new ProductRepository(database);
            _categoryRepository = new CategoryRepository(database);
            
            _products = new ObservableCollection<Product>();
            _allProducts = new List<Product>();
            
            ProductsDataGrid.ItemsSource = _products;
            
            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                // Load categories
                var categories = await _categoryRepository.GetActiveAsync();
                CategoryComboBox.ItemsSource = categories;
                CategoryComboBox.DisplayMemberPath = "Name";
                CategoryComboBox.SelectedValuePath = "Id";
                
                // Load products
                await LoadProducts();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement: {ex.Message}", 
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadProducts()
        {
            try
            {
                _allProducts = (await _productRepository.GetActiveAsync()).ToList();
                ApplyFilters();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des produits: {ex.Message}", 
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplyFilters()
        {
            var filteredProducts = _allProducts.AsEnumerable();

            // Search filter
            if (!string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                var searchTerm = SearchTextBox.Text.ToLower();
                filteredProducts = filteredProducts.Where(p => 
                    p.Name.ToLower().Contains(searchTerm) ||
                    p.Code.ToLower().Contains(searchTerm) ||
                    (p.Barcode?.ToLower().Contains(searchTerm) ?? false));
            }

            // Category filter
            if (CategoryComboBox.SelectedValue != null)
            {
                var categoryId = (int)CategoryComboBox.SelectedValue;
                filteredProducts = filteredProducts.Where(p => p.CategoryId == categoryId);
            }

            var results = filteredProducts.ToList();
            
            // Update UI
            _products.Clear();
            foreach (var product in results)
            {
                _products.Add(product);
            }
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void CategoryComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            SearchTextBox.Clear();
            CategoryComboBox.SelectedIndex = -1;
        }

        private void SelectButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Product product)
            {
                SelectedProduct = product;
                DialogResult = true;
                Close();
            }
        }

        private void ProductsDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (ProductsDataGrid.SelectedItem is Product product)
            {
                SelectedProduct = product;
                DialogResult = true;
                Close();
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
