{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\GestioDeStock\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{823CCBE1-A840-A6AC-0102-97AC7F36ACE5}|GestioDeStock\\GestioDeStock.csproj|c:\\users\\<USER>\\desktop\\gestiodestock\\gestiodestock\\views\\dialogs\\productsearchdialog.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{823CCBE1-A840-A6AC-0102-97AC7F36ACE5}|GestioDeStock\\GestioDeStock.csproj|solutionrelative:gestiodestock\\views\\dialogs\\productsearchdialog.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{823CCBE1-A840-A6AC-0102-97AC7F36ACE5}|GestioDeStock\\GestioDeStock.csproj|c:\\users\\<USER>\\desktop\\gestiodestock\\gestiodestock\\views\\dialogs\\manualentrydialog.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{823CCBE1-A840-A6AC-0102-97AC7F36ACE5}|GestioDeStock\\GestioDeStock.csproj|solutionrelative:gestiodestock\\views\\dialogs\\manualentrydialog.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{823CCBE1-A840-A6AC-0102-97AC7F36ACE5}|GestioDeStock\\GestioDeStock.csproj|c:\\users\\<USER>\\desktop\\gestiodestock\\gestiodestock\\data\\baserepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{823CCBE1-A840-A6AC-0102-97AC7F36ACE5}|GestioDeStock\\GestioDeStock.csproj|solutionrelative:gestiodestock\\data\\baserepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{823CCBE1-A840-A6AC-0102-97AC7F36ACE5}|GestioDeStock\\GestioDeStock.csproj|c:\\users\\<USER>\\desktop\\gestiodestock\\gestiodestock\\data\\repositories\\expenserepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{823CCBE1-A840-A6AC-0102-97AC7F36ACE5}|GestioDeStock\\GestioDeStock.csproj|solutionrelative:gestiodestock\\data\\repositories\\expenserepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{823CCBE1-A840-A6AC-0102-97AC7F36ACE5}|GestioDeStock\\GestioDeStock.csproj|c:\\users\\<USER>\\desktop\\gestiodestock\\gestiodestock\\app.config||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{823CCBE1-A840-A6AC-0102-97AC7F36ACE5}|GestioDeStock\\GestioDeStock.csproj|solutionrelative:gestiodestock\\app.config||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "ProductSearchDialog.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GestioDeStock\\GestioDeStock\\Views\\Dialogs\\ProductSearchDialog.xaml.cs", "RelativeDocumentMoniker": "GestioDeStock\\Views\\Dialogs\\ProductSearchDialog.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GestioDeStock\\GestioDeStock\\Views\\Dialogs\\ProductSearchDialog.xaml.cs", "RelativeToolTip": "GestioDeStock\\Views\\Dialogs\\ProductSearchDialog.xaml.cs", "ViewState": "AgIAABkAAAAAAAAAAAAIwCQAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-13T16:30:19.442Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "BaseRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GestioDeStock\\GestioDeStock\\Data\\BaseRepository.cs", "RelativeDocumentMoniker": "GestioDeStock\\Data\\BaseRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GestioDeStock\\GestioDeStock\\Data\\BaseRepository.cs", "RelativeToolTip": "GestioDeStock\\Data\\BaseRepository.cs", "ViewState": "AgIAACUAAAAAAAAAAAAnwDUAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-13T16:29:42.971Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "ManualEntryDialog.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GestioDeStock\\GestioDeStock\\Views\\Dialogs\\ManualEntryDialog.xaml.cs", "RelativeDocumentMoniker": "GestioDeStock\\Views\\Dialogs\\ManualEntryDialog.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GestioDeStock\\GestioDeStock\\Views\\Dialogs\\ManualEntryDialog.xaml.cs", "RelativeToolTip": "GestioDeStock\\Views\\Dialogs\\ManualEntryDialog.xaml.cs", "ViewState": "AgIAACkAAAAAAAAAAAAuwDMAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-13T16:29:40.126Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ExpenseRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GestioDeStock\\GestioDeStock\\Data\\Repositories\\ExpenseRepository.cs", "RelativeDocumentMoniker": "GestioDeStock\\Data\\Repositories\\ExpenseRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GestioDeStock\\GestioDeStock\\Data\\Repositories\\ExpenseRepository.cs", "RelativeToolTip": "GestioDeStock\\Data\\Repositories\\ExpenseRepository.cs", "ViewState": "AgIAAJQAAAAAAAAAAAAIwKQAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-13T16:29:27.451Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "App.config", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GestioDeStock\\GestioDeStock\\App.config", "RelativeDocumentMoniker": "GestioDeStock\\App.config", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GestioDeStock\\GestioDeStock\\App.config", "RelativeToolTip": "GestioDeStock\\App.config", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAABcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000601|", "WhenOpened": "2025-07-13T16:29:12.464Z", "EditorCaption": ""}]}]}]}