@echo off
echo ========================================
echo   Gestion de Stock - Mode Developpement
echo ========================================
echo.

cd GestioDeStock

echo Nettoyage des fichiers temporaires...
dotnet clean

echo.
echo Restauration des dependances...
dotnet restore

echo.
echo Construction en mode Debug...
dotnet build --configuration Debug

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Construction reussie! Demarrage en mode developpement...
    echo.
    echo CTRL+C pour arreter l'application
    echo.
    dotnet run --configuration Debug
) else (
    echo.
    echo Erreur lors de la construction!
    echo Verifiez les erreurs ci-dessus.
    pause
)
