﻿#pragma checksum "..\..\..\..\..\Views\Pages\PurchasesPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "CFBC1154E535CD1808571F23F93A7AAF3D04443F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GestioDeStock.Views.Pages {
    
    
    /// <summary>
    /// PurchasesPage
    /// </summary>
    public partial class PurchasesPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 24 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NewPurchaseButton;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SupplierComboBox;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ResultsCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PurchasesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalHTTextBlock;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalTVATextBlock;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalTTCTextBlock;
        
        #line default
        #line hidden
        
        
        #line 220 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CountTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GestioDeStock;V1.0.0.0;component/views/pages/purchasespage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.NewPurchaseButton = ((System.Windows.Controls.Button)(target));
            
            #line 28 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
            this.NewPurchaseButton.Click += new System.Windows.RoutedEventHandler(this.NewPurchaseButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 57 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SupplierComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 63 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
            this.SupplierComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SupplierComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.StatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 69 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
            this.StatusComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StatusComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 80 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ResultsCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 103 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.PurchasesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 123 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
            this.PurchasesDataGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.PurchasesDataGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 12:
            this.TotalHTTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TotalTVATextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.TotalTTCTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.CountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 9:
            
            #line 168 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewButton_Click);
            
            #line default
            #line hidden
            break;
            case 10:
            
            #line 175 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PrintButton_Click);
            
            #line default
            #line hidden
            break;
            case 11:
            
            #line 182 "..\..\..\..\..\Views\Pages\PurchasesPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

