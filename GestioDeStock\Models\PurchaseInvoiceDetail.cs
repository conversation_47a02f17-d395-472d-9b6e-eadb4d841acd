using System;

namespace GestioDeStock.Models
{
    public class PurchaseInvoiceDetail : BaseEntity
    {
        public int InvoiceId { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string ProductCode { get; set; } = string.Empty;
        public int UnitId { get; set; }
        public string UnitName { get; set; } = string.Empty;
        public decimal Quantity { get; set; }
        public decimal UnitCostHt { get; set; }
        public decimal UnitCostTtc { get; set; }
        public decimal TvaRate { get; set; } = 19.00m;
        public decimal LineTotalHt { get; set; }
        public decimal LineTotalTva { get; set; }
        public decimal LineTotalTtc { get; set; }
        public decimal DiscountPercentage { get; set; } = 0.00m;
        public decimal DiscountAmount { get; set; } = 0.00m;
        public DateTime? ExpiryDate { get; set; }
        public string? BatchNumber { get; set; }

        // Navigation properties
        public PurchaseInvoice Invoice { get; set; } = null!;
        public Product Product { get; set; } = null!;
        public Unit Unit { get; set; } = null!;
    }
}
