namespace GestioDeStock.Models
{
    public class SalesInvoiceDetail : BaseEntity
    {
        public int InvoiceId { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string ProductCode { get; set; } = string.Empty;
        public string? BarcodeUsed { get; set; }
        public int UnitId { get; set; }
        public string UnitName { get; set; } = string.Empty;
        public decimal Quantity { get; set; }
        public decimal UnitPriceHt { get; set; }
        public decimal UnitPriceTtc { get; set; }
        public decimal TvaRate { get; set; } = 19.00m;
        public decimal LineTotalHt { get; set; }
        public decimal LineTotalTva { get; set; }
        public decimal LineTotalTtc { get; set; }
        public decimal DiscountPercentage { get; set; } = 0.00m;
        public decimal DiscountAmount { get; set; } = 0.00m;
        public decimal CostPrice { get; set; } = 0.00m;
        public decimal ProfitAmount { get; set; } = 0.00m;
        public bool IsScaleProduct { get; set; } = false;
        public decimal? ScaleWeight { get; set; }

        // Navigation properties
        public SalesInvoice Invoice { get; set; } = null!;
        public Product Product { get; set; } = null!;
        public Unit Unit { get; set; } = null!;
    }
}
