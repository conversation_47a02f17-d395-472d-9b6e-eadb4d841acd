<Page x:Class="GestioDeStock.Views.Pages.CashPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="Gestion de la Caisse">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Style="{StaticResource CardStyle}">
            <DockPanel>
                <StackPanel DockPanel.Dock="Left">
                    <TextBlock Text="Gestion de la Caisse" FontSize="24" FontWeight="Bold"/>
                    <TextBlock Text="Gérer les sessions de caisse et les mouvements financiers" FontSize="14"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>

                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="OpenCashButton"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Content="OUVRIR CAISSE"
                          Margin="5,0"
                          Click="OpenCashButton_Click">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="CashRegister" Width="16" Height="16"
                                                           VerticalAlignment="Center" Margin="0,0,5,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <Button x:Name="CloseCashButton"
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Content="FERMER CAISSE"
                          Margin="5,0"
                          Click="CloseCashButton_Click">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="CashLock" Width="16" Height="16"
                                                           VerticalAlignment="Center" Margin="0,0,5,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>
                </StackPanel>
            </DockPanel>
        </materialDesign:Card>

        <!-- Current Session Info -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Current Session -->
            <materialDesign:Card Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0,0,10,0">
                <StackPanel>
                    <TextBlock Text="Session Actuelle" FontSize="16" FontWeight="Medium" Margin="0,0,0,15"/>

                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="N° Session:" FontSize="12"/>
                        <TextBlock x:Name="SessionNumberTextBlock" Grid.Row="0" Grid.Column="1" Text="Aucune" FontSize="12" FontWeight="Bold"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Ouverture:" FontSize="12"/>
                        <TextBlock x:Name="OpeningTimeTextBlock" Grid.Row="1" Grid.Column="1" Text="--:--" FontSize="12" FontWeight="Bold"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Montant Ouverture:" FontSize="12"/>
                        <TextBlock x:Name="OpeningAmountTextBlock" Grid.Row="2" Grid.Column="1" Text="0.00 DA" FontSize="12" FontWeight="Bold"/>

                        <TextBlock Grid.Row="3" Grid.Column="0" Text="Ventes Actuelles:" FontSize="12"/>
                        <TextBlock x:Name="CurrentSalesTextBlock" Grid.Row="3" Grid.Column="1" Text="0.00 DA" FontSize="12" FontWeight="Bold" Foreground="#4CAF50"/>

                        <TextBlock Grid.Row="4" Grid.Column="0" Text="Statut:" FontSize="12"/>
                        <TextBlock x:Name="SessionStatusTextBlock" Grid.Row="4" Grid.Column="1" Text="Fermée" FontSize="12" FontWeight="Bold" Foreground="#F44336"/>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Quick Actions -->
            <materialDesign:Card Grid.Column="1" Style="{StaticResource CardStyle}" Margin="10,0,0,0">
                <StackPanel>
                    <TextBlock Text="Actions Rapides" FontSize="16" FontWeight="Medium" Margin="0,0,0,15"/>

                    <Button x:Name="AddExpenseButton"
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Content="AJOUTER DÉPENSE"
                          Margin="0,5"
                          Click="AddExpenseButton_Click">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="CashMinus" Width="16" Height="16"
                                                           VerticalAlignment="Center" Margin="0,0,5,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <Button x:Name="AddIncomeButton"
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Content="AJOUTER RECETTE"
                          Margin="0,5"
                          Click="AddIncomeButton_Click">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="CashPlus" Width="16" Height="16"
                                                           VerticalAlignment="Center" Margin="0,0,5,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <Button x:Name="CashCountButton"
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Content="COMPTAGE CAISSE"
                          Margin="0,5"
                          Click="CashCountButton_Click">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Calculator" Width="16" Height="16"
                                                           VerticalAlignment="Center" Margin="0,0,5,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- Sessions History -->
        <materialDesign:Card Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Filters -->
                <DockPanel Grid.Row="0" Margin="0,0,0,10">
                    <TextBlock Text="Historique des Sessions" FontSize="16" FontWeight="Medium" DockPanel.Dock="Left"/>

                    <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                        <DatePicker x:Name="FromDatePicker"
                                  materialDesign:HintAssist.Hint="Du"
                                  Style="{StaticResource MaterialDesignFloatingHintDatePicker}"
                                  Margin="5,0"
                                  SelectedDateChanged="DatePicker_SelectedDateChanged"/>

                        <DatePicker x:Name="ToDatePicker"
                                  materialDesign:HintAssist.Hint="Au"
                                  Style="{StaticResource MaterialDesignFloatingHintDatePicker}"
                                  Margin="5,0"
                                  SelectedDateChanged="DatePicker_SelectedDateChanged"/>

                        <Button x:Name="RefreshButton"
                              Style="{StaticResource MaterialDesignIconButton}"
                              ToolTip="Actualiser"
                              Click="RefreshButton_Click">
                            <materialDesign:PackIcon Kind="Refresh"/>
                        </Button>
                    </StackPanel>
                </DockPanel>

                <!-- Summary -->
                <materialDesign:Card Grid.Row="1" Margin="0,0,0,10" Padding="15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                            <TextBlock Text="Sessions" FontSize="12" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="TotalSessionsTextBlock" Text="0" FontSize="16" FontWeight="Bold"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                            <TextBlock Text="Total Espèces" FontSize="12" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="TotalCashTextBlock" Text="0.00 DA" FontSize="16" FontWeight="Bold" Foreground="#4CAF50"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                            <TextBlock Text="Total Cartes" FontSize="12" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="TotalCardTextBlock" Text="0.00 DA" FontSize="16" FontWeight="Bold" Foreground="#2196F3"/>
                        </StackPanel>

                        <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                            <TextBlock Text="Total Ventes" FontSize="12" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="TotalSalesTextBlock" Text="0.00 DA" FontSize="16" FontWeight="Bold"/>
                        </StackPanel>
                    </Grid>
                </materialDesign:Card>

                <!-- Sessions List -->
                <DataGrid x:Name="SessionsDataGrid" Grid.Row="2"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        SelectionMode="Single"
                        MouseDoubleClick="SessionsDataGrid_MouseDoubleClick">

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="N° Session" Binding="{Binding SessionNumber}" Width="120"/>
                        <DataGridTextColumn Header="Utilisateur" Binding="{Binding UserName}" Width="150"/>
                        <DataGridTextColumn Header="Ouverture" Binding="{Binding OpenedAt, StringFormat='{}{0:dd/MM/yyyy HH:mm}'}" Width="130"/>
                        <DataGridTextColumn Header="Fermeture" Binding="{Binding ClosedAt, StringFormat='{}{0:dd/MM/yyyy HH:mm}'}" Width="130"/>
                        <DataGridTextColumn Header="Montant Ouv." Binding="{Binding OpeningAmount, StringFormat='{}{0:N2} DA'}" Width="120"/>
                        <DataGridTextColumn Header="Montant Ferm." Binding="{Binding ClosingAmount, StringFormat='{}{0:N2} DA'}" Width="120"/>
                        <DataGridTextColumn Header="Total Ventes" Binding="{Binding TotalSales, StringFormat='{}{0:N2} DA'}" Width="120"/>
                        <DataGridTextColumn Header="Espèces" Binding="{Binding TotalCash, StringFormat='{}{0:N2} DA'}" Width="100"/>
                        <DataGridTextColumn Header="Cartes" Binding="{Binding TotalCard, StringFormat='{}{0:N2} DA'}" Width="100"/>

                        <DataGridTemplateColumn Header="Statut" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="10" Padding="5,2" HorizontalAlignment="Center">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsClosed}" Value="True">
                                                        <Setter Property="Background" Value="#4CAF50"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding IsClosed}" Value="False">
                                                        <Setter Property="Background" Value="#FF9800"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock Text="{Binding IsClosed, Converter={StaticResource BoolToStatusConverter}}"
                                                 Foreground="White" FontSize="10" FontWeight="Bold"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="Actions" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                              ToolTip="Voir Détails"
                                              Click="ViewSessionButton_Click"
                                              Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="Eye" Width="16" Height="16"/>
                                        </Button>

                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                              ToolTip="Imprimer"
                                              Click="PrintSessionButton_Click"
                                              Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="Printer" Width="16" Height="16"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Page>
