<Page x:Class="GestioDeStock.Views.Pages.StockPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="Gestion du Stock">
    <Grid Margin="20">
        <materialDesign:Card Style="{StaticResource CardStyle}">
            <StackPanel>
                <TextBlock Text="Gestion du Stock" FontSize="24" FontWeight="Bold" Margin="0,0,0,20"/>
                <TextBlock Text="Interface de gestion du stock à implémenter" FontSize="16" 
                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</Page>
