<Page x:Class="GestioDeStock.Views.Pages.StockPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="Gestion du Stock">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Style="{StaticResource CardStyle}">
            <DockPanel>
                <StackPanel DockPanel.Dock="Left">
                    <TextBlock Text="Gestion du Stock" FontSize="24" FontWeight="Bold"/>
                    <TextBlock Text="Suivi des stocks, mouvements et inventaires" FontSize="14"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>

                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="StockAdjustmentButton"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Content="AJUSTEMENT"
                          Margin="5,0"
                          Click="StockAdjustmentButton_Click">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="TuneVariant" Width="16" Height="16"
                                                           VerticalAlignment="Center" Margin="0,0,5,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>

                    <Button x:Name="InventoryButton"
                          Style="{StaticResource MaterialDesignOutlinedButton}"
                          Content="INVENTAIRE"
                          Margin="5,0"
                          Click="InventoryButton_Click">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ClipboardList" Width="16" Height="16"
                                                           VerticalAlignment="Center" Margin="0,0,5,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>
                </StackPanel>
            </DockPanel>
        </materialDesign:Card>

        <!-- Filters and Summary -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Filters -->
            <materialDesign:Card Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0,0,10,0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBox x:Name="SearchTextBox" Grid.Column="0"
                           materialDesign:HintAssist.Hint="Rechercher par nom ou code..."
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           Margin="0,0,10,0"
                           TextChanged="SearchTextBox_TextChanged"/>

                    <ComboBox x:Name="WarehouseComboBox" Grid.Column="1"
                            materialDesign:HintAssist.Hint="Magasin"
                            Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                            Margin="0,0,10,0"
                            SelectionChanged="WarehouseComboBox_SelectionChanged"/>

                    <ComboBox x:Name="StockStatusComboBox" Grid.Column="2"
                            materialDesign:HintAssist.Hint="État du Stock"
                            Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                            Margin="0,0,10,0"
                            SelectionChanged="StockStatusComboBox_SelectionChanged">
                        <ComboBoxItem Content="Tous"/>
                        <ComboBoxItem Content="Stock Normal"/>
                        <ComboBoxItem Content="Stock Faible"/>
                        <ComboBoxItem Content="Rupture de Stock"/>
                        <ComboBoxItem Content="Stock Élevé"/>
                    </ComboBox>

                    <Button x:Name="RefreshButton" Grid.Column="3"
                          Style="{StaticResource MaterialDesignIconButton}"
                          ToolTip="Actualiser"
                          Click="RefreshButton_Click">
                        <materialDesign:PackIcon Kind="Refresh"/>
                    </Button>
                </Grid>
            </materialDesign:Card>

            <!-- Summary Cards -->
            <materialDesign:Card Grid.Column="1" Style="{StaticResource CardStyle}" Margin="10,0,0,0">
                <StackPanel>
                    <TextBlock Text="Résumé" FontSize="16" FontWeight="Medium" Margin="0,0,0,15"/>

                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Produits:" FontSize="12"/>
                        <TextBlock x:Name="TotalProductsTextBlock" Grid.Row="0" Grid.Column="1" Text="0" FontSize="12" FontWeight="Bold"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Valeur Stock:" FontSize="12"/>
                        <TextBlock x:Name="StockValueTextBlock" Grid.Row="1" Grid.Column="1" Text="0 DA" FontSize="12" FontWeight="Bold"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Stock Faible:" FontSize="12" Foreground="#FF9800"/>
                        <TextBlock x:Name="LowStockTextBlock" Grid.Row="2" Grid.Column="1" Text="0" FontSize="12" FontWeight="Bold" Foreground="#FF9800"/>

                        <TextBlock Grid.Row="3" Grid.Column="0" Text="Ruptures:" FontSize="12" Foreground="#F44336"/>
                        <TextBlock x:Name="OutOfStockTextBlock" Grid.Row="3" Grid.Column="1" Text="0" FontSize="12" FontWeight="Bold" Foreground="#F44336"/>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- Stock List -->
        <materialDesign:Card Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <DockPanel Grid.Row="0" Margin="0,0,0,10">
                    <TextBlock x:Name="ResultsCountTextBlock" Text="0 produits trouvés"
                             FontWeight="Medium" DockPanel.Dock="Left"/>

                    <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                        <Button x:Name="ExportButton"
                              Style="{StaticResource MaterialDesignOutlinedButton}"
                              Content="EXPORTER" Margin="5,0"
                              Click="ExportButton_Click">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FileExport" Width="16" Height="16"
                                                               VerticalAlignment="Center" Margin="0,0,5,0"/>
                                        <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <Button x:Name="PrintButton"
                              Style="{StaticResource MaterialDesignOutlinedButton}"
                              Content="IMPRIMER" Margin="5,0"
                              Click="PrintButton_Click">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Printer" Width="16" Height="16"
                                                               VerticalAlignment="Center" Margin="0,0,5,0"/>
                                        <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>
                    </StackPanel>
                </DockPanel>

                <DataGrid x:Name="StockDataGrid" Grid.Row="1"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        SelectionMode="Single"
                        MouseDoubleClick="StockDataGrid_MouseDoubleClick">

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Code" Binding="{Binding ProductCode}" Width="100"/>
                        <DataGridTextColumn Header="Produit" Binding="{Binding ProductName}" Width="200"/>
                        <DataGridTextColumn Header="Magasin" Binding="{Binding WarehouseName}" Width="120"/>
                        <DataGridTextColumn Header="Stock Dispo." Binding="{Binding QuantityAvailable, StringFormat='{}{0:N2}'}" Width="100"/>
                        <DataGridTextColumn Header="Stock Réservé" Binding="{Binding QuantityReserved, StringFormat='{}{0:N2}'}" Width="100"/>
                        <DataGridTextColumn Header="Coût Moyen" Binding="{Binding AverageCost, StringFormat='{}{0:N2} DA'}" Width="120"/>
                        <DataGridTextColumn Header="Valeur Stock" Binding="{Binding StockValue, StringFormat='{}{0:N2} DA'}" Width="120"/>
                        <DataGridTextColumn Header="Alerte Min" Binding="{Binding MinStockAlert, StringFormat='{}{0:N2}'}" Width="100"/>

                        <DataGridTemplateColumn Header="État" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="10" Padding="5,2" HorizontalAlignment="Center">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding StockStatus}" Value="Normal">
                                                        <Setter Property="Background" Value="#4CAF50"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding StockStatus}" Value="Faible">
                                                        <Setter Property="Background" Value="#FF9800"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding StockStatus}" Value="Élevé">
                                                        <Setter Property="Background" Value="#2196F3"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding StockStatus}" Value="Rupture">
                                                        <Setter Property="Background" Value="#F44336"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock Text="{Binding StockStatus}" Foreground="White" FontSize="10" FontWeight="Bold"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="Actions" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                              ToolTip="Ajuster Stock"
                                              Click="AdjustStockButton_Click"
                                              Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="TuneVariant" Width="16" Height="16"/>
                                        </Button>

                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                              ToolTip="Historique"
                                              Click="HistoryButton_Click"
                                              Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="History" Width="16" Height="16"/>
                                        </Button>

                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                              ToolTip="Transfert"
                                              Click="TransferButton_Click"
                                              Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="SwapHorizontal" Width="16" Height="16"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Page>
