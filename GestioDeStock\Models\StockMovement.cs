using System;

namespace GestioDeStock.Models
{
    public class StockMovement : BaseEntity
    {
        public int ProductId { get; set; }
        public int WarehouseId { get; set; }
        public MovementType MovementType { get; set; }
        public ReferenceType ReferenceType { get; set; }
        public int? ReferenceId { get; set; }
        public string? ReferenceNumber { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitCost { get; set; } = 0.00m;
        public decimal TotalCost { get; set; } = 0.00m;
        public decimal StockBefore { get; set; } = 0.00m;
        public decimal StockAfter { get; set; } = 0.00m;
        public DateTime MovementDate { get; set; } = DateTime.Now;
        public string? Notes { get; set; }

        // Navigation properties
        public Product Product { get; set; } = null!;
        public Warehouse Warehouse { get; set; } = null!;
    }

    public enum MovementType
    {
        Entrée,
        Sortie,
        Transfert,
        Ajustement,
        Inventaire
    }

    public enum ReferenceType
    {
        Vente,
        Achat,
        Transfert,
        Ajustement,
        Inventaire,
        Initial
    }
}
