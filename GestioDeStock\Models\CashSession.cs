using System;
using System.Collections.Generic;

namespace GestioDeStock.Models
{
    public class CashSession : BaseEntity
    {
        public string SessionNumber { get; set; } = string.Empty;
        public int UserId { get; set; }
        public int WarehouseId { get; set; }
        public decimal OpeningAmount { get; set; } = 0.00m;
        public decimal? ClosingAmount { get; set; }
        public decimal TotalSales { get; set; } = 0.00m;
        public decimal TotalCash { get; set; } = 0.00m;
        public decimal TotalCard { get; set; } = 0.00m;
        public decimal TotalCheck { get; set; } = 0.00m;
        public decimal TotalCredit { get; set; } = 0.00m;
        public DateTime OpenedAt { get; set; } = DateTime.Now;
        public DateTime? ClosedAt { get; set; }
        public bool IsClosed { get; set; } = false;
        public string? Notes { get; set; }

        // Navigation properties
        public User User { get; set; } = null!;
        public Warehouse Warehouse { get; set; } = null!;
        public List<SalesInvoice> SalesInvoices { get; set; } = new List<SalesInvoice>();
    }
}
