<Window x:Class="GestioDeStock.Views.Dialogs.BarcodeDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Gestion des Codes-barres" 
        Height="600" Width="700"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="Gestion des Codes-barres" FontSize="24" FontWeight="Bold"/>
            <TextBlock x:Name="ProductInfoTextBlock" FontSize="14" 
                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
        </StackPanel>
        
        <!-- Add New Barcode -->
        <materialDesign:Card Grid.Row="1" Padding="20" Margin="0,0,0,15">
            <StackPanel>
                <TextBlock Text="Ajouter un Code-barres" FontSize="16" FontWeight="Medium" Margin="0,0,0,15"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Barcode Input -->
                    <TextBox x:Name="NewBarcodeTextBox" Grid.Column="0"
                           materialDesign:HintAssist.Hint="Code-barres"
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           Margin="0,0,10,0"/>
                    
                    <!-- Unit Quantity -->
                    <TextBox x:Name="UnitQuantityTextBox" Grid.Column="1"
                           materialDesign:HintAssist.Hint="Quantité"
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           Text="1.000"
                           Margin="0,0,10,0"/>
                    
                    <!-- Unit Price -->
                    <TextBox x:Name="UnitPriceTextBox" Grid.Column="2"
                           materialDesign:HintAssist.Hint="Prix Unitaire"
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           Margin="0,0,10,0"/>
                    
                    <!-- Add Button -->
                    <Button x:Name="AddBarcodeButton" Grid.Column="3"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Content="AJOUTER"
                          Click="AddBarcodeButton_Click"/>
                </Grid>
                
                <!-- Scale Barcode Options -->
                <StackPanel Margin="0,15,0,0">
                    <CheckBox x:Name="IsScaleBarcodeCheckBox" Content="Code-barres de Balance" 
                            Checked="IsScaleBarcodeCheckBox_Checked"
                            Unchecked="IsScaleBarcodeCheckBox_Unchecked"/>
                    
                    <Grid x:Name="ScaleBarcodePanel" Visibility="Collapsed" Margin="0,10,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Prefix -->
                        <ComboBox x:Name="ScalePrefixComboBox" Grid.Column="0"
                                materialDesign:HintAssist.Hint="Préfixe"
                                Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                                Margin="0,0,10,0">
                            <ComboBoxItem Content="22" IsSelected="True"/>
                            <ComboBoxItem Content="02"/>
                        </ComboBox>
                        
                        <!-- Product Code -->
                        <TextBox x:Name="ScaleProductCodeTextBox" Grid.Column="1"
                               materialDesign:HintAssist.Hint="Code Produit"
                               Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                               MaxLength="4"
                               Margin="0,0,10,0"/>
                        
                        <!-- Weight/Price -->
                        <TextBox x:Name="ScaleValueTextBox" Grid.Column="2"
                               materialDesign:HintAssist.Hint="Poids/Prix"
                               Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                               Margin="0,0,10,0"/>
                        
                        <!-- Generate Button -->
                        <Button x:Name="GenerateScaleBarcodeButton" Grid.Column="3"
                              Style="{StaticResource MaterialDesignOutlinedButton}"
                              Content="GÉNÉRER"
                              Click="GenerateScaleBarcodeButton_Click"/>
                    </Grid>
                </StackPanel>
            </StackPanel>
        </materialDesign:Card>
        
        <!-- Barcodes List -->
        <materialDesign:Card Grid.Row="2" Padding="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="Codes-barres Existants" FontSize="16" FontWeight="Medium" Margin="0,0,0,15"/>
                
                <DataGrid x:Name="BarcodesDataGrid" Grid.Row="1"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        SelectionMode="Single">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Code-barres" Binding="{Binding Barcode}" Width="200"/>
                        <DataGridTextColumn Header="Quantité" Binding="{Binding UnitQuantity, StringFormat='{}{0:N3}'}" Width="100"/>
                        <DataGridTextColumn Header="Prix Unit." Binding="{Binding UnitPrice, StringFormat='{}{0:N2} DA'}" Width="100"/>
                        
                        <DataGridTemplateColumn Header="Principal" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <CheckBox IsChecked="{Binding IsPrimary}" 
                                            IsEnabled="False"
                                            HorizontalAlignment="Center"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        
                        <DataGridTemplateColumn Header="Balance" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <CheckBox IsChecked="{Binding IsScaleBarcode}" 
                                            IsEnabled="False"
                                            HorizontalAlignment="Center"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        
                        <DataGridTemplateColumn Header="Actions" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Style="{StaticResource MaterialDesignIconButton}" 
                                              ToolTip="Définir comme Principal"
                                              Click="SetPrimaryButton_Click"
                                              Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="Star" Width="16" Height="16"/>
                                        </Button>
                                        
                                        <Button Style="{StaticResource MaterialDesignIconButton}" 
                                              ToolTip="Supprimer"
                                              Click="DeleteBarcodeButton_Click"
                                              Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                        </Button>
                                        
                                        <Button Style="{StaticResource MaterialDesignIconButton}" 
                                              ToolTip="Imprimer"
                                              Click="PrintBarcodeButton_Click"
                                              Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="Printer" Width="16" Height="16"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="PrintAllButton" 
                  Content="IMPRIMER TOUT" 
                  Style="{StaticResource MaterialDesignOutlinedButton}"
                  Margin="0,0,10,0"
                  Click="PrintAllButton_Click"/>
            
            <Button x:Name="CloseButton" 
                  Content="FERMER" 
                  Style="{StaticResource MaterialDesignRaisedButton}"
                  Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
