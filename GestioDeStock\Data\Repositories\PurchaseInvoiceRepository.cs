using Dapper;
using GestioDeStock.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace GestioDeStock.Data.Repositories
{
    public class PurchaseInvoiceRepository : BaseRepository<PurchaseInvoice>
    {
        public PurchaseInvoiceRepository(DatabaseConnection database) : base(database, "purchase_invoices")
        {
        }

        public override async Task<int> AddAsync(PurchaseInvoice entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                INSERT INTO purchase_invoices (invoice_number, supplier_invoice_number, invoice_type, 
                    supplier_id, warehouse_id, user_id, invoice_date, due_date, subtotal_ht, total_tva, 
                    total_ttc, discount_amount, discount_percentage, timbre_amount, final_amount, 
                    paid_amount, remaining_amount, payment_method, payment_status, status, notes, 
                    created_by, updated_by)
                VALUES (@InvoiceNumber, @SupplierInvoiceNumber, @InvoiceType, @SupplierId, @WarehouseId, 
                    @UserId, @InvoiceDate, @DueDate, @SubtotalHt, @TotalTva, @TotalTtc, @DiscountAmount, 
                    @DiscountPercentage, @TimbreAmount, @FinalAmount, @PaidAmount, @RemainingAmount, 
                    @PaymentMethod, @PaymentStatus, @Status, @Notes, @CreatedBy, @UpdatedBy);
                SELECT LAST_INSERT_ID();";
            
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(PurchaseInvoice entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE purchase_invoices SET 
                    invoice_number = @InvoiceNumber,
                    supplier_invoice_number = @SupplierInvoiceNumber,
                    invoice_type = @InvoiceType,
                    supplier_id = @SupplierId,
                    warehouse_id = @WarehouseId,
                    user_id = @UserId,
                    invoice_date = @InvoiceDate,
                    due_date = @DueDate,
                    subtotal_ht = @SubtotalHt,
                    total_tva = @TotalTva,
                    total_ttc = @TotalTtc,
                    discount_amount = @DiscountAmount,
                    discount_percentage = @DiscountPercentage,
                    timbre_amount = @TimbreAmount,
                    final_amount = @FinalAmount,
                    paid_amount = @PaidAmount,
                    remaining_amount = @RemainingAmount,
                    payment_method = @PaymentMethod,
                    payment_status = @PaymentStatus,
                    status = @Status,
                    notes = @Notes,
                    updated_at = NOW(),
                    updated_by = @UpdatedBy
                WHERE id = @Id";
            
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public override async Task<IEnumerable<PurchaseInvoice>> SearchAsync(string searchTerm)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT pi.*, s.name as SupplierName, u.full_name as UserName
                FROM purchase_invoices pi
                LEFT JOIN suppliers s ON pi.supplier_id = s.id
                LEFT JOIN users u ON pi.user_id = u.id
                WHERE pi.invoice_number LIKE @SearchTerm 
                   OR pi.supplier_invoice_number LIKE @SearchTerm
                   OR s.name LIKE @SearchTerm
                ORDER BY pi.invoice_date DESC";
            
            return await connection.QueryAsync<PurchaseInvoice>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<string> GetNextInvoiceNumberAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT CONCAT('AC', LPAD(COALESCE(MAX(CAST(SUBSTRING(invoice_number, 3) AS UNSIGNED)), 0) + 1, 6, '0'))
                FROM purchase_invoices 
                WHERE invoice_number LIKE 'AC%'";
            
            return await connection.QuerySingleAsync<string>(sql);
        }

        public async Task<IEnumerable<PurchaseInvoice>> GetTodaysPurchasesAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT * FROM purchase_invoices 
                WHERE DATE(invoice_date) = CURDATE() 
                ORDER BY invoice_date DESC";
            
            return await connection.QueryAsync<PurchaseInvoice>(sql);
        }

        public async Task<decimal> GetTotalPurchasesAmountAsync(DateTime fromDate, DateTime toDate)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT COALESCE(SUM(final_amount), 0) 
                FROM purchase_invoices 
                WHERE invoice_date BETWEEN @FromDate AND @ToDate 
                  AND status != 'Annulé'";
            
            return await connection.QuerySingleAsync<decimal>(sql, new { FromDate = fromDate, ToDate = toDate });
        }

        public async Task<IEnumerable<PurchaseInvoice>> GetPendingInvoicesAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT * FROM purchase_invoices 
                WHERE payment_status IN ('Non Payé', 'Partiellement Payé')
                  AND status != 'Annulé'
                ORDER BY due_date ASC";
            
            return await connection.QueryAsync<PurchaseInvoice>(sql);
        }
    }
}
