using GestioDeStock.Data;
using GestioDeStock.Data.Repositories;
using GestioDeStock.Services;
using System.Configuration;
using System.Windows;
using System.Windows.Input;

namespace GestioDeStock.Views
{
    public partial class LoginWindow : Window
    {
        private readonly AuthenticationService _authService;

        public LoginWindow()
        {
            InitializeComponent();
            
            // Configuration de la base de données
            var config = ConfigurationService.Instance;
            var connectionString = config.GetConnectionString();

            var database = new DatabaseConnection(connectionString);
            var userRepository = new UserRepository(database);
            _authService = new AuthenticationService(userRepository);
            
            // Focus sur le champ username
            UsernameTextBox.Focus();
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            await PerformLogin();
        }

        private async Task PerformLogin()
        {
            try
            {
                // Désactiver le bouton pendant la connexion
                LoginButton.IsEnabled = false;
                ErrorTextBlock.Visibility = Visibility.Collapsed;

                var username = UsernameTextBox.Text.Trim();
                var password = PasswordBox.Password;

                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    ShowError("Veuillez saisir le nom d'utilisateur et le mot de passe.");
                    return;
                }

                var loginSuccess = await _authService.LoginAsync(username, password);

                if (loginSuccess)
                {
                    // Ouvrir la fenêtre principale
                    var mainWindow = new MainWindow(_authService);
                    mainWindow.Show();
                    this.Close();
                }
                else
                {
                    ShowError("Nom d'utilisateur ou mot de passe incorrect.");
                }
            }
            catch (Exception ex)
            {
                ShowError($"Erreur de connexion: {ex.Message}");
            }
            finally
            {
                LoginButton.IsEnabled = true;
            }
        }

        private void ShowError(string message)
        {
            ErrorTextBlock.Text = message;
            ErrorTextBlock.Visibility = Visibility.Visible;
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                _ = PerformLogin();
            }
            base.OnKeyDown(e);
        }
    }
}
