<Page x:Class="GestioDeStock.Views.Pages.PurchasesPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="Gestion des Achats">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Style="{StaticResource CardStyle}">
            <DockPanel>
                <StackPanel DockPanel.Dock="Left">
                    <TextBlock Text="Gestion des Achats" FontSize="24" FontWeight="Bold"/>
                    <TextBlock Text="Gérer les factures d'achat et les réceptions" FontSize="14"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>

                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="NewPurchaseButton"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Content="NOUVEL ACHAT"
                          Margin="10,0,0,0"
                          Click="NewPurchaseButton_Click">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Plus" Width="16" Height="16"
                                                           VerticalAlignment="Center" Margin="0,0,5,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>
                </StackPanel>
            </DockPanel>
        </materialDesign:Card>

        <!-- Filters -->
        <materialDesign:Card Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBox x:Name="SearchTextBox" Grid.Column="0"
                       materialDesign:HintAssist.Hint="Rechercher par numéro, fournisseur..."
                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                       Margin="0,0,10,0"
                       TextChanged="SearchTextBox_TextChanged"/>

                <ComboBox x:Name="SupplierComboBox" Grid.Column="1"
                        materialDesign:HintAssist.Hint="Fournisseur"
                        Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                        Margin="0,0,10,0"
                        SelectionChanged="SupplierComboBox_SelectionChanged"/>

                <ComboBox x:Name="StatusComboBox" Grid.Column="2"
                        materialDesign:HintAssist.Hint="Statut"
                        Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                        Margin="0,0,10,0"
                        SelectionChanged="StatusComboBox_SelectionChanged">
                    <ComboBoxItem Content="Tous"/>
                    <ComboBoxItem Content="Brouillon"/>
                    <ComboBoxItem Content="Confirmé"/>
                    <ComboBoxItem Content="Reçu"/>
                    <ComboBoxItem Content="Annulé"/>
                </ComboBox>

                <Button x:Name="RefreshButton" Grid.Column="3"
                      Style="{StaticResource MaterialDesignIconButton}"
                      ToolTip="Actualiser"
                      Click="RefreshButton_Click">
                    <materialDesign:PackIcon Kind="Refresh"/>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Purchases List -->
        <materialDesign:Card Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <DockPanel Grid.Row="0" Margin="0,0,0,10">
                    <TextBlock x:Name="ResultsCountTextBlock" Text="0 achats trouvés"
                             FontWeight="Medium" DockPanel.Dock="Left"/>

                    <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                        <Button x:Name="ExportButton"
                              Style="{StaticResource MaterialDesignOutlinedButton}"
                              Content="EXPORTER" Margin="5,0"
                              Click="ExportButton_Click">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FileExport" Width="16" Height="16"
                                                               VerticalAlignment="Center" Margin="0,0,5,0"/>
                                        <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>
                    </StackPanel>
                </DockPanel>

                <DataGrid x:Name="PurchasesDataGrid" Grid.Row="1"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        SelectionMode="Single"
                        MouseDoubleClick="PurchasesDataGrid_MouseDoubleClick">

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="N° Facture" Binding="{Binding InvoiceNumber}" Width="120"/>
                        <DataGridTextColumn Header="N° Fournisseur" Binding="{Binding SupplierInvoiceNumber}" Width="120"/>
                        <DataGridTextColumn Header="Fournisseur" Binding="{Binding SupplierName}" Width="200"/>
                        <DataGridTextColumn Header="Date" Binding="{Binding InvoiceDate, StringFormat='{}{0:dd/MM/yyyy}'}" Width="100"/>
                        <DataGridTextColumn Header="Montant HT" Binding="{Binding SubtotalHt, StringFormat='{}{0:N2} DA'}" Width="120"/>
                        <DataGridTextColumn Header="TVA" Binding="{Binding TotalTva, StringFormat='{}{0:N2} DA'}" Width="100"/>
                        <DataGridTextColumn Header="Total TTC" Binding="{Binding TotalTtc, StringFormat='{}{0:N2} DA'}" Width="120"/>

                        <DataGridTemplateColumn Header="Statut" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="10" Padding="5,2" HorizontalAlignment="Center">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Status}" Value="Brouillon">
                                                        <Setter Property="Background" Value="#FF9800"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="Confirmé">
                                                        <Setter Property="Background" Value="#2196F3"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="Reçu">
                                                        <Setter Property="Background" Value="#4CAF50"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="Annulé">
                                                        <Setter Property="Background" Value="#F44336"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock Text="{Binding Status}" Foreground="White" FontSize="10" FontWeight="Bold"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="Actions" Width="150">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                              ToolTip="Voir/Modifier"
                                              Click="ViewButton_Click"
                                              Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="Eye" Width="16" Height="16"/>
                                        </Button>

                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                              ToolTip="Imprimer"
                                              Click="PrintButton_Click"
                                              Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="Printer" Width="16" Height="16"/>
                                        </Button>

                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                              ToolTip="Supprimer"
                                              Click="DeleteButton_Click"
                                              Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Summary -->
                <materialDesign:Card Grid.Row="2" Margin="0,10,0,0" Padding="15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                            <TextBlock Text="Total HT" FontSize="12" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="TotalHTTextBlock" Text="0.00 DA" FontSize="16" FontWeight="Bold"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                            <TextBlock Text="Total TVA" FontSize="12" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="TotalTVATextBlock" Text="0.00 DA" FontSize="16" FontWeight="Bold"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                            <TextBlock Text="Total TTC" FontSize="12" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="TotalTTCTextBlock" Text="0.00 DA" FontSize="16" FontWeight="Bold"/>
                        </StackPanel>

                        <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                            <TextBlock Text="Nombre" FontSize="12" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="CountTextBlock" Text="0" FontSize="16" FontWeight="Bold"/>
                        </StackPanel>
                    </Grid>
                </materialDesign:Card>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Page>
