using GestioDeStock.Data;
using GestioDeStock.Data.Repositories;
using GestioDeStock.Models;
using GestioDeStock.Services;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;

namespace GestioDeStock.Views.Pages
{
    public partial class SuppliersPage : Page
    {
        private readonly AuthenticationService _authService;
        private readonly SupplierRepository _supplierRepository;
        private ObservableCollection<Supplier> _suppliers;
        private List<Supplier> _allSuppliers;

        public SuppliersPage(AuthenticationService authService)
        {
            InitializeComponent();
            _authService = authService;

            var config = ConfigurationService.Instance;
            var database = new DatabaseConnection(config.GetConnectionString());
            _supplierRepository = new SupplierRepository(database);

            _suppliers = new ObservableCollection<Supplier>();
            _allSuppliers = new List<Supplier>();

            SuppliersDataGrid.ItemsSource = _suppliers;

            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                // Set default status filter
                StatusComboBox.SelectedIndex = 0; // All

                // Load suppliers
                await LoadSuppliers();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des données: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadSuppliers()
        {
            try
            {
                _allSuppliers = (await _supplierRepository.GetAllAsync()).ToList();
                ApplyFilters();
                UpdateSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des fournisseurs: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplyFilters()
        {
            var filteredSuppliers = _allSuppliers.AsEnumerable();

            // Search filter
            if (!string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                var searchTerm = SearchTextBox.Text.ToLower();
                filteredSuppliers = filteredSuppliers.Where(s =>
                    s.Name.ToLower().Contains(searchTerm) ||
                    (s.Phone?.ToLower().Contains(searchTerm) ?? false) ||
                    (s.Email?.ToLower().Contains(searchTerm) ?? false) ||
                    s.Code.ToLower().Contains(searchTerm));
            }

            // Status filter
            if (StatusComboBox.SelectedIndex > 0)
            {
                var statusText = ((ComboBoxItem)StatusComboBox.SelectedItem).Content.ToString();
                switch (statusText)
                {
                    case "Actifs":
                        filteredSuppliers = filteredSuppliers.Where(s => s.IsActive);
                        break;
                    case "Inactifs":
                        filteredSuppliers = filteredSuppliers.Where(s => !s.IsActive);
                        break;
                    case "Avec Dettes":
                        filteredSuppliers = filteredSuppliers.Where(s => s.CurrentBalance < 0);
                        break;
                }
            }

            var results = filteredSuppliers.ToList();

            // Update UI
            _suppliers.Clear();
            foreach (var supplier in results)
            {
                _suppliers.Add(supplier);
            }

            ResultsCountTextBlock.Text = $"{results.Count} fournisseurs trouvés";
        }

        private void UpdateSummary()
        {
            var totalSuppliers = _allSuppliers.Count;
            var activeSuppliers = _allSuppliers.Count(s => s.IsActive);
            var totalDebt = Math.Abs(_allSuppliers.Where(s => s.CurrentBalance < 0).Sum(s => s.CurrentBalance));
            var totalCreditLimit = _allSuppliers.Sum(s => s.CreditLimit);

            TotalSuppliersTextBlock.Text = totalSuppliers.ToString();
            ActiveSuppliersTextBlock.Text = activeSuppliers.ToString();
            TotalDebtTextBlock.Text = $"{totalDebt:N2} DA";
            TotalCreditLimitTextBlock.Text = $"{totalCreditLimit:N2} DA";
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void StatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadSuppliers();
        }

        private async void NewSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new Views.Dialogs.AddSupplierDialog(_authService);
            if (dialog.ShowDialog() == true && dialog.NewSupplier != null)
            {
                await LoadSuppliers();
                MessageBox.Show($"Fournisseur '{dialog.NewSupplier.Name}' ajouté avec succès!", "Succès",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Supplier supplier)
            {
                // TODO: Open edit supplier dialog
                MessageBox.Show($"Modifier le fournisseur {supplier.Name}", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void AccountButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Supplier supplier)
            {
                // TODO: Open supplier account dialog
                MessageBox.Show($"Compte du fournisseur {supplier.Name}", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void HistoryButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Supplier supplier)
            {
                // TODO: Open supplier history dialog
                MessageBox.Show($"Historique du fournisseur {supplier.Name}", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Supplier supplier)
            {
                var result = MessageBox.Show(
                    $"Êtes-vous sûr de vouloir supprimer le fournisseur '{supplier.Name}' ?",
                    "Confirmation de suppression",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await _supplierRepository.DeleteAsync(supplier.Id);
                        await LoadSuppliers();
                        MessageBox.Show("Fournisseur supprimé avec succès.", "Succès",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Erreur lors de la suppression: {ex.Message}",
                                      "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void SuppliersDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (SuppliersDataGrid.SelectedItem is Supplier supplier)
            {
                // TODO: Open supplier details dialog
                MessageBox.Show($"Détails du fournisseur {supplier.Name}", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement export functionality
            MessageBox.Show("Fonctionnalité d'export à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
