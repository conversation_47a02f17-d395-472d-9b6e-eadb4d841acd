using GestioDeStock.Data;
using GestioDeStock.Data.Repositories;
using GestioDeStock.Models;
using GestioDeStock.Services;
using System.Windows;
using System.Windows.Controls;

namespace GestioDeStock.Views.Pages
{
    public partial class SettingsPage : Page
    {
        private readonly AuthenticationService _authService;
        private readonly CompanyInfoRepository _companyRepository;
        private readonly WarehouseRepository _warehouseRepository;
        private CompanyInfo? _companyInfo;

        public SettingsPage(AuthenticationService authService)
        {
            InitializeComponent();
            _authService = authService;

            var config = ConfigurationService.Instance;
            var database = new DatabaseConnection(config.GetConnectionString());
            _companyRepository = new CompanyInfoRepository(database);
            _warehouseRepository = new WarehouseRepository(database);

            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                // Load company information
                await LoadCompanyInfo();

                // Load warehouses for default selection
                var warehouses = await _warehouseRepository.GetActiveWarehousesAsync();
                DefaultWarehouseComboBox.ItemsSource = warehouses;
                DefaultWarehouseComboBox.DisplayMemberPath = "Name";
                DefaultWarehouseComboBox.SelectedValuePath = "Id";

                // Load application settings from configuration
                LoadApplicationSettings();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des paramètres: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadCompanyInfo()
        {
            try
            {
                _companyInfo = await _companyRepository.GetCompanyInfoAsync();

                if (_companyInfo != null)
                {
                    CompanyNameTextBox.Text = _companyInfo.Name;
                    CompanyPhoneTextBox.Text = _companyInfo.Phone ?? "";
                    CompanyEmailTextBox.Text = _companyInfo.Email ?? "";
                    CompanyWebsiteTextBox.Text = _companyInfo.Website ?? "";
                    CompanyAddressTextBox.Text = _companyInfo.Address ?? "";
                    CompanyNifTextBox.Text = _companyInfo.Nif ?? "";
                    CompanyNisTextBox.Text = _companyInfo.Nis ?? "";
                    CompanyRcTextBox.Text = _companyInfo.Rc ?? "";
                    CompanyArticleTextBox.Text = _companyInfo.Article ?? "";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des informations de l'entreprise: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadApplicationSettings()
        {
            try
            {
                var config = ConfigurationService.Instance;

                // Load settings from configuration
                BackupPathTextBox.Text = config.GetSetting("BackupPath", @"C:\Backups\GestionStock");
                AutoBackupCheckBox.IsChecked = bool.Parse(config.GetSetting("AutoBackupEnabled", "false"));
                BackupIntervalTextBox.Text = config.GetSetting("AutoBackupIntervalHours", "6");
                PrintLogoCheckBox.IsChecked = bool.Parse(config.GetSetting("PrintLogo", "true"));
                PrintFooterCheckBox.IsChecked = bool.Parse(config.GetSetting("PrintFooter", "true"));

                // Set currency
                CurrencyComboBox.SelectedIndex = 0; // Default to DA
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des paramètres de l'application: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                await SaveCompanyInfo();
                SaveApplicationSettings();

                MessageBox.Show("Paramètres enregistrés avec succès!", "Succès",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'enregistrement: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task SaveCompanyInfo()
        {
            try
            {
                if (_companyInfo == null)
                {
                    _companyInfo = new CompanyInfo
                    {
                        CreatedBy = _authService.CurrentUser?.Id ?? 0,
                        UpdatedBy = _authService.CurrentUser?.Id ?? 0
                    };
                }
                else
                {
                    _companyInfo.UpdatedBy = _authService.CurrentUser?.Id ?? 0;
                }

                _companyInfo.Name = CompanyNameTextBox.Text.Trim();
                _companyInfo.Phone = CompanyPhoneTextBox.Text.Trim();
                _companyInfo.Email = CompanyEmailTextBox.Text.Trim();
                _companyInfo.Website = CompanyWebsiteTextBox.Text.Trim();
                _companyInfo.Address = CompanyAddressTextBox.Text.Trim();
                _companyInfo.Nif = CompanyNifTextBox.Text.Trim();
                _companyInfo.Nis = CompanyNisTextBox.Text.Trim();
                _companyInfo.Rc = CompanyRcTextBox.Text.Trim();
                _companyInfo.Article = CompanyArticleTextBox.Text.Trim();

                if (_companyInfo.Id == 0)
                {
                    await _companyRepository.AddAsync(_companyInfo);
                }
                else
                {
                    await _companyRepository.UpdateAsync(_companyInfo);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Erreur lors de l'enregistrement des informations de l'entreprise: {ex.Message}");
            }
        }

        private void SaveApplicationSettings()
        {
            try
            {
                var config = ConfigurationService.Instance;

                // Save application settings
                config.SetSetting("BackupPath", BackupPathTextBox.Text.Trim());
                config.SetSetting("AutoBackupEnabled", (AutoBackupCheckBox.IsChecked ?? false).ToString());
                config.SetSetting("AutoBackupIntervalHours", BackupIntervalTextBox.Text.Trim());
                config.SetSetting("PrintLogo", (PrintLogoCheckBox.IsChecked ?? true).ToString());
                config.SetSetting("PrintFooter", (PrintFooterCheckBox.IsChecked ?? true).ToString());

                if (DefaultWarehouseComboBox.SelectedValue != null)
                {
                    config.SetSetting("DefaultWarehouseId", DefaultWarehouseComboBox.SelectedValue.ToString() ?? "1");
                }

                // Save configuration
                config.SaveConfiguration();
            }
            catch (Exception ex)
            {
                throw new Exception($"Erreur lors de l'enregistrement des paramètres de l'application: {ex.Message}");
            }
        }

        private void TestConnectionButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // TODO: Test database connection
                MessageBox.Show("Test de connexion à implémenter", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur de connexion: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BackupDatabaseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // TODO: Implement database backup
                MessageBox.Show("Sauvegarde de base de données à implémenter", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de la sauvegarde: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RestoreDatabaseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // TODO: Implement database restore
                MessageBox.Show("Restauration de base de données à implémenter", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de la restauration: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OptimizeDatabaseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // TODO: Implement database optimization
                MessageBox.Show("Optimisation de base de données à implémenter", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'optimisation: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
