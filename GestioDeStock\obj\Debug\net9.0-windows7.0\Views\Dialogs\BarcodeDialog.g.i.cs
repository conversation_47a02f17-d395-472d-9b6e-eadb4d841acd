﻿#pragma checksum "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "CED0A0A56E2D531B8D5BE6826BDAD4AE1703D881"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GestioDeStock.Views.Dialogs {
    
    
    /// <summary>
    /// BarcodeDialog
    /// </summary>
    public partial class BarcodeDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 23 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProductInfoTextBlock;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NewBarcodeTextBox;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UnitQuantityTextBox;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UnitPriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddBarcodeButton;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsScaleBarcodeCheckBox;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ScaleBarcodePanel;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ScalePrefixComboBox;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ScaleProductCodeTextBox;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ScaleValueTextBox;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GenerateScaleBarcodeButton;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid BarcodesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintAllButton;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GestioDeStock;V1.0.0.0;component/views/dialogs/barcodedialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ProductInfoTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.NewBarcodeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.UnitQuantityTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.UnitPriceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.AddBarcodeButton = ((System.Windows.Controls.Button)(target));
            
            #line 63 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
            this.AddBarcodeButton.Click += new System.Windows.RoutedEventHandler(this.AddBarcodeButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.IsScaleBarcodeCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 69 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
            this.IsScaleBarcodeCheckBox.Checked += new System.Windows.RoutedEventHandler(this.IsScaleBarcodeCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 70 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
            this.IsScaleBarcodeCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.IsScaleBarcodeCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ScaleBarcodePanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 8:
            this.ScalePrefixComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.ScaleProductCodeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.ScaleValueTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.GenerateScaleBarcodeButton = ((System.Windows.Controls.Button)(target));
            
            #line 106 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
            this.GenerateScaleBarcodeButton.Click += new System.Windows.RoutedEventHandler(this.GenerateScaleBarcodeButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.BarcodesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 16:
            this.PrintAllButton = ((System.Windows.Controls.Button)(target));
            
            #line 193 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
            this.PrintAllButton.Click += new System.Windows.RoutedEventHandler(this.PrintAllButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 198 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 13:
            
            #line 160 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SetPrimaryButton_Click);
            
            #line default
            #line hidden
            break;
            case 14:
            
            #line 167 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteBarcodeButton_Click);
            
            #line default
            #line hidden
            break;
            case 15:
            
            #line 174 "..\..\..\..\..\Views\Dialogs\BarcodeDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PrintBarcodeButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

