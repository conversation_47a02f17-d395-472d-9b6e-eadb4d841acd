{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\GestioDeStock\\GestioDeStock\\GestioDeStock.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\GestioDeStock\\GestioDeStock\\GestioDeStock.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\GestioDeStock\\GestioDeStock\\GestioDeStock.csproj", "projectName": "GestioDeStock", "projectPath": "C:\\Users\\<USER>\\Desktop\\GestioDeStock\\GestioDeStock\\GestioDeStock.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\GestioDeStock\\GestioDeStock\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 19.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0-windows7.0"], "sources": {"C:\\Program Files (x86)\\DevExpress 19.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows7.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows7.0", "dependencies": {"BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "Dapper": {"target": "Package", "version": "[2.1.66, )"}, "MaterialDesignColors": {"target": "Package", "version": "[5.2.1, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[5.2.1, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.7, )"}, "MySql.Data": {"target": "Package", "version": "[9.3.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Configuration.ConfigurationManager": {"target": "Package", "version": "[9.0.7, )"}, "System.Drawing.Common": {"target": "Package", "version": "[9.0.7, )"}, "ZXing.Net": {"target": "Package", "version": "[0.16.10, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}