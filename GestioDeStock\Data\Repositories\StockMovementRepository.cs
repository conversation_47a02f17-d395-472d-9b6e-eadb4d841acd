using Dapper;
using GestioDeStock.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace GestioDeStock.Data.Repositories
{
    public class StockMovementRepository : BaseRepository<StockMovement>
    {
        public StockMovementRepository(DatabaseConnection database) : base(database, "stock_movements")
        {
        }

        public override async Task<int> AddAsync(StockMovement entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                INSERT INTO stock_movements (product_id, warehouse_id, movement_type, reference_type, 
                    reference_id, reference_number, quantity, unit_cost, total_cost, stock_before, 
                    stock_after, movement_date, notes, created_by, updated_by)
                VALUES (@ProductId, @WarehouseId, @MovementType, @ReferenceType, @ReferenceId, 
                    @ReferenceNumber, @Quantity, @UnitCost, @TotalCost, @StockBefore, @StockAfter, 
                    @MovementDate, @Notes, @CreatedBy, @UpdatedBy);
                SELECT LAST_INSERT_ID();";
            
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(StockMovement entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE stock_movements SET 
                    product_id = @ProductId,
                    warehouse_id = @WarehouseId,
                    movement_type = @MovementType,
                    reference_type = @ReferenceType,
                    reference_id = @ReferenceId,
                    reference_number = @ReferenceNumber,
                    quantity = @Quantity,
                    unit_cost = @UnitCost,
                    total_cost = @TotalCost,
                    stock_before = @StockBefore,
                    stock_after = @StockAfter,
                    movement_date = @MovementDate,
                    notes = @Notes,
                    updated_at = NOW(),
                    updated_by = @UpdatedBy
                WHERE id = @Id";
            
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public override async Task<IEnumerable<StockMovement>> SearchAsync(string searchTerm)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT sm.*, p.name as ProductName, w.name as WarehouseName
                FROM stock_movements sm
                LEFT JOIN products p ON sm.product_id = p.id
                LEFT JOIN warehouses w ON sm.warehouse_id = w.id
                WHERE p.name LIKE @SearchTerm 
                   OR p.code LIKE @SearchTerm
                   OR sm.reference_number LIKE @SearchTerm
                ORDER BY sm.movement_date DESC";
            
            return await connection.QueryAsync<StockMovement>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<IEnumerable<StockMovement>> GetMovementsByProductAsync(int productId, int? warehouseId = null)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT sm.*, p.name as ProductName, w.name as WarehouseName
                FROM stock_movements sm
                LEFT JOIN products p ON sm.product_id = p.id
                LEFT JOIN warehouses w ON sm.warehouse_id = w.id
                WHERE sm.product_id = @ProductId";
            
            if (warehouseId.HasValue)
            {
                sql += " AND sm.warehouse_id = @WarehouseId";
            }
            
            sql += " ORDER BY sm.movement_date DESC";
            
            return await connection.QueryAsync<StockMovement>(sql, new { ProductId = productId, WarehouseId = warehouseId });
        }

        public async Task<IEnumerable<StockMovement>> GetMovementsByDateRangeAsync(DateTime fromDate, DateTime toDate, int? warehouseId = null)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT sm.*, p.name as ProductName, w.name as WarehouseName
                FROM stock_movements sm
                LEFT JOIN products p ON sm.product_id = p.id
                LEFT JOIN warehouses w ON sm.warehouse_id = w.id
                WHERE sm.movement_date BETWEEN @FromDate AND @ToDate";
            
            if (warehouseId.HasValue)
            {
                sql += " AND sm.warehouse_id = @WarehouseId";
            }
            
            sql += " ORDER BY sm.movement_date DESC";
            
            return await connection.QueryAsync<StockMovement>(sql, new { FromDate = fromDate, ToDate = toDate, WarehouseId = warehouseId });
        }

        public async Task<IEnumerable<StockMovement>> GetMovementsByTypeAsync(MovementType movementType, int? warehouseId = null)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT sm.*, p.name as ProductName, w.name as WarehouseName
                FROM stock_movements sm
                LEFT JOIN products p ON sm.product_id = p.id
                LEFT JOIN warehouses w ON sm.warehouse_id = w.id
                WHERE sm.movement_type = @MovementType";
            
            if (warehouseId.HasValue)
            {
                sql += " AND sm.warehouse_id = @WarehouseId";
            }
            
            sql += " ORDER BY sm.movement_date DESC";
            
            return await connection.QueryAsync<StockMovement>(sql, new { MovementType = movementType.ToString(), WarehouseId = warehouseId });
        }

        public async Task<decimal> GetTotalMovementValueAsync(DateTime fromDate, DateTime toDate, MovementType? movementType = null)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT COALESCE(SUM(total_cost), 0) 
                FROM stock_movements 
                WHERE movement_date BETWEEN @FromDate AND @ToDate";
            
            if (movementType.HasValue)
            {
                sql += " AND movement_type = @MovementType";
            }
            
            return await connection.QuerySingleAsync<decimal>(sql, new { FromDate = fromDate, ToDate = toDate, MovementType = movementType?.ToString() });
        }
    }
}
