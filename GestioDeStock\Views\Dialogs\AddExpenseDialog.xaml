<Window x:Class="GestioDeStock.Views.Dialogs.AddExpenseDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        Title="Ajouter Dépense/Recette" 
        Height="450" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <materialDesign:PackIcon x:Name="HeaderIcon" Kind="CashMinus" Width="48" Height="48" 
                                   Foreground="{DynamicResource PrimaryHueMidBrush}" 
                                   HorizontalAlignment="Center" Margin="0,0,0,10"/>
            <TextBlock x:Name="HeaderTextBlock" Text="Nouvelle Dépense" FontSize="18" FontWeight="Bold" 
                     HorizontalAlignment="Center"/>
        </StackPanel>
        
        <!-- Form -->
        <StackPanel Grid.Row="1">
            
            <!-- Type Selection -->
            <StackPanel>
                <TextBlock Text="Type d'opération:" FontSize="14" FontWeight="Medium" Margin="0,0,0,10"/>
                <StackPanel Orientation="Horizontal">
                    <RadioButton x:Name="ExpenseRadioButton" Content="Dépense" 
                               Style="{StaticResource MaterialDesignRadioButton}"
                               IsChecked="True" Margin="0,0,20,0"
                               Checked="TypeRadioButton_Checked"/>
                    <RadioButton x:Name="IncomeRadioButton" Content="Recette" 
                               Style="{StaticResource MaterialDesignRadioButton}"
                               Checked="TypeRadioButton_Checked"/>
                </StackPanel>
            </StackPanel>
            
            <ComboBox x:Name="CategoryComboBox"
                    materialDesign:HintAssist.Hint="Catégorie *"
                    Style="{StaticResource MaterialDesignFloatingHintComboBox}"/>
            
            <TextBox x:Name="DescriptionTextBox"
                   materialDesign:HintAssist.Hint="Description *"
                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"/>
            
            <TextBox x:Name="AmountTextBox"
                   materialDesign:HintAssist.Hint="Montant (DA) *"
                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                   TextChanged="AmountTextBox_TextChanged"/>

            <DatePicker x:Name="TransactionDatePicker"
                      materialDesign:HintAssist.Hint="Date de transaction"
                      Style="{StaticResource MaterialDesignFloatingHintDatePicker}"
                      SelectedDate="{x:Static sys:DateTime.Now}"/>
            
            <!-- Payment Method -->
            <ComboBox x:Name="PaymentMethodComboBox"
                    materialDesign:HintAssist.Hint="Mode de paiement"
                    Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                    SelectionChanged="PaymentMethodComboBox_SelectionChanged">
                <ComboBoxItem Content="Espèces" IsSelected="True"/>
                <ComboBoxItem Content="Chèque"/>
                <ComboBoxItem Content="Virement"/>
                <ComboBoxItem Content="Carte bancaire"/>
            </ComboBox>
            
            <!-- Bank Account (for non-cash payments) -->
            <ComboBox x:Name="BankAccountComboBox"
                    materialDesign:HintAssist.Hint="Compte bancaire"
                    Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                    Visibility="Collapsed"/>
            
            <!-- Check Details (for check payments) -->
            <Grid x:Name="CheckDetailsGrid" Visibility="Collapsed">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <TextBox x:Name="CheckNumberTextBox" Grid.Column="0"
                       materialDesign:HintAssist.Hint="N° Chèque"
                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                       Margin="0,0,10,0"/>
                
                <DatePicker x:Name="CheckDatePicker" Grid.Column="1"
                          materialDesign:HintAssist.Hint="Date chèque"
                          Style="{StaticResource MaterialDesignFloatingHintDatePicker}"
                          Margin="10,0,0,0"/>
            </Grid>
            
            <TextBox x:Name="NotesTextBox"
                   materialDesign:HintAssist.Hint="Notes (optionnel)"
                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                   AcceptsReturn="True"
                   TextWrapping="Wrap"
                   MinLines="2"
                   MaxLines="3"/>
            
            <!-- Summary -->
            <Border BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="1" 
                  CornerRadius="4" Padding="15" Margin="0,10,0,0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock x:Name="SummaryLabelTextBlock" Grid.Column="0" Text="Total Dépense:" FontSize="16" FontWeight="Medium"/>
                    <TextBlock x:Name="SummaryAmountTextBlock" Grid.Column="1" Text="0.00 DA" FontSize="16" FontWeight="Bold" 
                             Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                </Grid>
            </Border>
            
        </StackPanel>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="SaveButton" 
                  Style="{StaticResource MaterialDesignRaisedButton}"
                  Content="ENREGISTRER" 
                  Margin="0,0,10,0"
                  Click="SaveButton_Click"
                  IsEnabled="False"/>
            
            <Button x:Name="CancelButton" 
                  Style="{StaticResource MaterialDesignOutlinedButton}"
                  Content="ANNULER" 
                  Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
