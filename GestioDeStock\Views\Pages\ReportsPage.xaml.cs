using GestioDeStock.Data;
using GestioDeStock.Data.Repositories;
using GestioDeStock.Services;
using System.Windows;
using System.Windows.Controls;

namespace GestioDeStock.Views.Pages
{
    public partial class ReportsPage : Page
    {
        private readonly AuthenticationService _authService;
        private readonly SalesInvoiceRepository _salesRepository;
        private readonly PurchaseInvoiceRepository _purchaseRepository;
        private readonly ProductStockRepository _stockRepository;
        private readonly ExpenseRepository _expenseRepository;
        private readonly ProductRepository _productRepository;
        private readonly SupplierRepository _supplierRepository;

        public ReportsPage(AuthenticationService authService)
        {
            InitializeComponent();
            _authService = authService;

            var config = ConfigurationService.Instance;
            var database = new DatabaseConnection(config.GetConnectionString());
            _salesRepository = new SalesInvoiceRepository(database);
            _purchaseRepository = new PurchaseInvoiceRepository(database);
            _stockRepository = new ProductStockRepository(database);
            _expenseRepository = new ExpenseRepository(database);
            _productRepository = new ProductRepository(database);
            _supplierRepository = new SupplierRepository(database);

            // Set default date range (current month)
            var firstDayOfMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            FromDatePicker.SelectedDate = firstDayOfMonth;
            ToDatePicker.SelectedDate = DateTime.Today;

            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                var fromDate = FromDatePicker.SelectedDate ?? DateTime.Today.AddDays(-30);
                var toDate = ToDatePicker.SelectedDate ?? DateTime.Today;

                await LoadSalesData(fromDate, toDate);
                await LoadPurchaseData(fromDate, toDate);
                await LoadStockData();
                await LoadFinancialData(fromDate, toDate);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des données: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadSalesData(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var totalAmount = await _salesRepository.GetTotalSalesAmountAsync(fromDate, toDate);
                var salesCount = await _salesRepository.GetSalesCountAsync(fromDate, toDate);
                var averageSale = salesCount > 0 ? totalAmount / salesCount : 0;

                TotalSalesAmountTextBlock.Text = $"{totalAmount:N2} DA";
                TotalSalesCountTextBlock.Text = salesCount.ToString();
                AverageSaleTextBlock.Text = $"{averageSale:N2} DA";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des données de ventes: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadPurchaseData(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var totalAmount = await _purchaseRepository.GetTotalPurchasesAmountAsync(fromDate, toDate);
                var purchasesCount = (await _purchaseRepository.GetAllAsync()).Count(p =>
                    p.InvoiceDate >= fromDate && p.InvoiceDate <= toDate);
                var activeSuppliers = (await _supplierRepository.GetActiveSuppliersAsync()).Count();

                TotalPurchasesAmountTextBlock.Text = $"{totalAmount:N2} DA";
                TotalPurchasesCountTextBlock.Text = purchasesCount.ToString();
                ActiveSuppliersTextBlock.Text = activeSuppliers.ToString();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des données d'achats: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadStockData()
        {
            try
            {
                var stockValue = await _stockRepository.GetTotalStockValueAsync();
                var lowStockCount = await _stockRepository.GetLowStockCountAsync();
                var totalProducts = (await _productRepository.GetAllAsync()).Count(p => p.IsActive);

                StockValueTextBlock.Text = $"{stockValue:N2} DA";
                LowStockCountTextBlock.Text = lowStockCount.ToString();
                TotalProductsTextBlock.Text = totalProducts.ToString();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des données de stock: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadFinancialData(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var totalIncome = await _expenseRepository.GetTotalExpensesAsync(fromDate, toDate, Models.ExpenseType.Recette);
                var totalExpenses = await _expenseRepository.GetTotalExpensesAsync(fromDate, toDate, Models.ExpenseType.Dépense);
                var netProfit = totalIncome - totalExpenses;

                TotalIncomeTextBlock.Text = $"{totalIncome:N2} DA";
                TotalExpensesTextBlock.Text = $"{totalExpenses:N2} DA";
                NetProfitTextBlock.Text = $"{netProfit:N2} DA";

                // Change color based on profit/loss
                if (netProfit >= 0)
                {
                    NetProfitTextBlock.Foreground = System.Windows.Media.Brushes.Green;
                }
                else
                {
                    NetProfitTextBlock.Foreground = System.Windows.Media.Brushes.Red;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des données financières: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            if (FromDatePicker.SelectedDate.HasValue && ToDatePicker.SelectedDate.HasValue)
            {
                LoadData();
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadData();
        }

        // Sales Reports
        private void SalesReportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Rapport détaillé des ventes à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void SalesByProductButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Rapport des ventes par produit à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // Purchase Reports
        private void PurchasesReportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Rapport détaillé des achats à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void PurchasesBySupplierButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Rapport des achats par fournisseur à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // Stock Reports
        private void StockReportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("État du stock à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void StockMovementsButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Rapport des mouvements de stock à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void InventoryReportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Rapport d'inventaire à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // Financial Reports
        private void FinancialReportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Rapport financier à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CashFlowButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Flux de trésorerie à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
