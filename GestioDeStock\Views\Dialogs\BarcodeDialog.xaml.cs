using GestioDeStock.Models;
using GestioDeStock.Services;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace GestioDeStock.Views.Dialogs
{
    public partial class BarcodeDialog : Window
    {
        private readonly Product _product;
        private readonly BarcodeService _barcodeService;
        private ObservableCollection<ProductBarcode> _barcodes;

        public BarcodeDialog(Product product)
        {
            InitializeComponent();
            _product = product;
            _barcodeService = new BarcodeService();
            _barcodes = new ObservableCollection<ProductBarcode>();
            
            BarcodesDataGrid.ItemsSource = _barcodes;
            
            InitializeDialog();
        }

        private void InitializeDialog()
        {
            ProductInfoTextBlock.Text = $"Produit: {_product.Code} - {_product.Name}";
            
            // Set default values
            UnitPriceTextBox.Text = _product.PrixVenteTtc.ToString("F2");
            ScaleProductCodeTextBox.Text = _product.Code.Length >= 4 ? _product.Code.Substring(0, 4) : _product.Code.PadRight(4, '0');
            
            // Load existing barcodes
            LoadBarcodes();
        }

        private void LoadBarcodes()
        {
            // TODO: Load from database
            // For now, add primary barcode if exists
            _barcodes.Clear();
            
            // Add sample data for demonstration
            if (!string.IsNullOrEmpty(_product.Code))
            {
                _barcodes.Add(new ProductBarcode
                {
                    ProductId = _product.Id,
                    Barcode = _product.Code,
                    IsPrimary = true,
                    IsScaleBarcode = false,
                    UnitQuantity = 1.000m,
                    UnitPrice = _product.PrixVenteTtc
                });
            }
        }

        private void AddBarcodeButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(NewBarcodeTextBox.Text))
                {
                    MessageBox.Show("Veuillez saisir un code-barres.", "Validation", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!decimal.TryParse(UnitQuantityTextBox.Text, out decimal quantity) || quantity <= 0)
                {
                    MessageBox.Show("La quantité doit être un nombre positif.", "Validation", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                decimal? unitPrice = null;
                if (!string.IsNullOrWhiteSpace(UnitPriceTextBox.Text))
                {
                    if (decimal.TryParse(UnitPriceTextBox.Text, out decimal price))
                        unitPrice = price;
                }

                // Check if barcode already exists
                if (_barcodes.Any(b => b.Barcode == NewBarcodeTextBox.Text))
                {
                    MessageBox.Show("Ce code-barres existe déjà.", "Validation", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var newBarcode = new ProductBarcode
                {
                    ProductId = _product.Id,
                    Barcode = NewBarcodeTextBox.Text.Trim(),
                    IsPrimary = _barcodes.Count == 0,
                    IsScaleBarcode = IsScaleBarcodeCheckBox.IsChecked == true,
                    UnitQuantity = quantity,
                    UnitPrice = unitPrice
                };

                _barcodes.Add(newBarcode);

                // TODO: Save to database

                // Clear inputs
                NewBarcodeTextBox.Clear();
                UnitQuantityTextBox.Text = "1.000";
                UnitPriceTextBox.Text = _product.PrixVenteTtc.ToString("F2");

                MessageBox.Show("Code-barres ajouté avec succès.", "Succès", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'ajout: {ex.Message}", "Erreur", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void GenerateScaleBarcodeButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var prefix = ((ComboBoxItem)ScalePrefixComboBox.SelectedItem)?.Content?.ToString() ?? "22";
                var productCode = ScaleProductCodeTextBox.Text.Trim();
                
                if (string.IsNullOrWhiteSpace(productCode) || productCode.Length != 4)
                {
                    MessageBox.Show("Le code produit doit faire exactement 4 caractères.", "Validation", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (!decimal.TryParse(ScaleValueTextBox.Text, out decimal value) || value <= 0)
                {
                    MessageBox.Show("La valeur doit être un nombre positif.", "Validation", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                bool isWeightBased = prefix == "22";
                var barcode = _barcodeService.GenerateScaleBarcode(productCode, value, isWeightBased);
                
                NewBarcodeTextBox.Text = barcode;
                IsScaleBarcodeCheckBox.IsChecked = true;
                
                if (isWeightBased)
                {
                    UnitQuantityTextBox.Text = value.ToString("F3");
                    UnitPriceTextBox.Text = (_product.PrixVenteTtc * value).ToString("F2");
                }
                else
                {
                    UnitQuantityTextBox.Text = "1.000";
                    UnitPriceTextBox.Text = value.ToString("F2");
                }

                MessageBox.Show("Code-barres de balance généré avec succès.", "Succès", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de la génération: {ex.Message}", "Erreur", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void IsScaleBarcodeCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            ScaleBarcodePanel.Visibility = Visibility.Visible;
        }

        private void IsScaleBarcodeCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            ScaleBarcodePanel.Visibility = Visibility.Collapsed;
        }

        private void SetPrimaryButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is ProductBarcode barcode)
            {
                // Remove primary flag from all barcodes
                foreach (var b in _barcodes)
                {
                    b.IsPrimary = false;
                }

                // Set this barcode as primary
                barcode.IsPrimary = true;

                // TODO: Update in database

                // Refresh the DataGrid
                BarcodesDataGrid.Items.Refresh();

                MessageBox.Show("Code-barres principal mis à jour.", "Succès", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void DeleteBarcodeButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is ProductBarcode barcode)
            {
                var result = MessageBox.Show(
                    $"Êtes-vous sûr de vouloir supprimer le code-barres '{barcode.Barcode}' ?",
                    "Confirmation de suppression",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    _barcodes.Remove(barcode);

                    // TODO: Delete from database

                    // If this was the primary barcode and there are others, set the first one as primary
                    if (barcode.IsPrimary && _barcodes.Count > 0)
                    {
                        _barcodes.First().IsPrimary = true;
                        BarcodesDataGrid.Items.Refresh();
                    }

                    MessageBox.Show("Code-barres supprimé avec succès.", "Succès", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void PrintBarcodeButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is ProductBarcode barcode)
            {
                // TODO: Implement barcode printing
                MessageBox.Show($"Impression du code-barres: {barcode.Barcode}", "Information", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void PrintAllButton_Click(object sender, RoutedEventArgs e)
        {
            if (_barcodes.Count == 0)
            {
                MessageBox.Show("Aucun code-barres à imprimer.", "Information", 
                              MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            // TODO: Implement printing all barcodes
            MessageBox.Show($"Impression de {_barcodes.Count} codes-barres.", "Information", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
