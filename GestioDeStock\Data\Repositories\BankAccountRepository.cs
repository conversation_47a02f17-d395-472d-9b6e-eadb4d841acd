using Dapper;
using GestioDeStock.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace GestioDeStock.Data.Repositories
{
    public class BankAccountRepository : BaseRepository<BankAccount>
    {
        public BankAccountRepository(DatabaseConnection database) : base(database, "bank_accounts")
        {
        }

        public override async Task<int> AddAsync(BankAccount entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                INSERT INTO bank_accounts (account_name, bank_name, account_number, rib, swift_code, 
                    opening_balance, current_balance, is_active, created_by, updated_by)
                VALUES (@AccountName, @BankName, @AccountNumber, @Rib, @SwiftCode, @OpeningBalance, 
                    @CurrentBalance, @IsActive, @CreatedBy, @UpdatedBy);
                SELECT LAST_INSERT_ID();";
            
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(BankAccount entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE bank_accounts SET 
                    account_name = @AccountName,
                    bank_name = @BankName,
                    account_number = @AccountNumber,
                    rib = @Rib,
                    swift_code = @SwiftCode,
                    opening_balance = @OpeningBalance,
                    current_balance = @CurrentBalance,
                    is_active = @IsActive,
                    updated_at = NOW(),
                    updated_by = @UpdatedBy
                WHERE id = @Id";
            
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public override async Task<IEnumerable<BankAccount>> SearchAsync(string searchTerm)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT * FROM bank_accounts 
                WHERE account_name LIKE @SearchTerm 
                   OR bank_name LIKE @SearchTerm 
                   OR account_number LIKE @SearchTerm
                ORDER BY account_name";
            
            return await connection.QueryAsync<BankAccount>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<IEnumerable<BankAccount>> GetActiveAccountsAsync()
        {
            return await GetActiveAsync();
        }

        public async Task<BankAccount?> GetByAccountNumberAsync(string accountNumber)
        {
            using var connection = _database.CreateConnection();
            var sql = "SELECT * FROM bank_accounts WHERE account_number = @AccountNumber";
            return await connection.QueryFirstOrDefaultAsync<BankAccount>(sql, new { AccountNumber = accountNumber });
        }

        public async Task<bool> UpdateBalanceAsync(int accountId, decimal amount)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE bank_accounts 
                SET current_balance = current_balance + @Amount,
                    updated_at = NOW()
                WHERE id = @AccountId";
            
            var affectedRows = await connection.ExecuteAsync(sql, new { AccountId = accountId, Amount = amount });
            return affectedRows > 0;
        }

        public async Task<decimal> GetTotalBalanceAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = "SELECT COALESCE(SUM(current_balance), 0) FROM bank_accounts WHERE is_active = 1";
            return await connection.QuerySingleAsync<decimal>(sql);
        }

        public async Task<IEnumerable<BankAccount>> GetAccountsWithPositiveBalanceAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT * FROM bank_accounts 
                WHERE current_balance > 0 AND is_active = 1 
                ORDER BY current_balance DESC";
            
            return await connection.QueryAsync<BankAccount>(sql);
        }

        public async Task<IEnumerable<BankAccount>> GetAccountsWithNegativeBalanceAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT * FROM bank_accounts 
                WHERE current_balance < 0 AND is_active = 1 
                ORDER BY current_balance ASC";
            
            return await connection.QueryAsync<BankAccount>(sql);
        }
    }
}
