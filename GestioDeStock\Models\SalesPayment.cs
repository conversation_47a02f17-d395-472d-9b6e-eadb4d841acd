using System;

namespace GestioDeStock.Models
{
    public class SalesPayment : BaseEntity
    {
        public int InvoiceId { get; set; }
        public PaymentMethodType PaymentMethod { get; set; }
        public decimal Amount { get; set; }
        public int? BankAccountId { get; set; }
        public string? CheckNumber { get; set; }
        public DateTime? CheckDate { get; set; }
        public string? CheckBank { get; set; }
        public string? ReferenceNumber { get; set; }
        public string? Notes { get; set; }
        public DateTime PaymentDate { get; set; } = DateTime.Now;

        // Navigation properties
        public SalesInvoice Invoice { get; set; } = null!;
        public BankAccount? BankAccount { get; set; }
    }

    public enum PaymentMethodType
    {
        Espèce,
        Carte,
        Chèque,
        Virement,
        Crédit
    }
}
