﻿#pragma checksum "..\..\..\..\..\Views\Pages\SettingsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "96FC56B0DC9DF35E7C9C5908E768268270BD4424"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GestioDeStock.Views.Pages {
    
    
    /// <summary>
    /// SettingsPage
    /// </summary>
    public partial class SettingsPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 22 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyPhoneTextBox;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyEmailTextBox;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyWebsiteTextBox;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyAddressTextBox;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyNifTextBox;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyNisTextBox;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyRcTextBox;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyArticleTextBox;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DefaultWarehouseComboBox;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CurrencyComboBox;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BackupPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoBackupCheckBox;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BackupIntervalTextBox;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox PrintLogoCheckBox;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox PrintFooterCheckBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GestioDeStock;V1.0.0.0;component/views/pages/settingspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 26 "..\..\..\..\..\Views\Pages\SettingsPage.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.CompanyNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.CompanyPhoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.CompanyEmailTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.CompanyWebsiteTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.CompanyAddressTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.CompanyNifTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.CompanyNisTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.CompanyRcTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.CompanyArticleTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.DefaultWarehouseComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 12:
            this.CurrencyComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 13:
            this.BackupPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.AutoBackupCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 15:
            this.BackupIntervalTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.PrintLogoCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 17:
            this.PrintFooterCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

