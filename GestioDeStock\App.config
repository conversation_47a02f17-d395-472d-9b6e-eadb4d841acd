<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <connectionStrings>
        <add name="DefaultConnection" 
             connectionString="Server=localhost;Database=gestion_stock;Uid=root;Pwd=********;CharSet=utf8mb4;" 
             providerName="MySql.Data.MySqlClient" />
    </connectionStrings>
    
    <appSettings>
        <add key="CompanyName" value="Mon Épicerie" />
        <add key="ApplicationVersion" value="1.0.0" />
        <add key="DefaultLanguage" value="fr-FR" />
        <add key="DefaultCurrency" value="DA" />
        <add key="DefaultTVARate" value="19.00" />
        <add key="BackupPath" value="C:\Backups\GestionStock" />
        <add key="ReportsPath" value="C:\Reports\GestionStock" />
    </appSettings>
    
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
    </startup>
</configuration>
