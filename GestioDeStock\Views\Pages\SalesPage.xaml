<Page x:Class="GestioDeStock.Views.Pages.SalesPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="Point de Vente">

    <Grid Margin="20">
        <materialDesign:Card Style="{StaticResource CardStyle}">
            <StackPanel>
                <TextBlock Text="Point de Vente" FontSize="24" FontWeight="Bold" Margin="0,0,0,20"/>
                <TextBlock Text="Interface de point de vente à implémenter" FontSize="16" 
                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</Page>
