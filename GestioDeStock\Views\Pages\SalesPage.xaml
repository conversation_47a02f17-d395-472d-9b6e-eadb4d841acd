<Page x:Class="GestioDeStock.Views.Pages.SalesPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="Point de Vente">

    <Grid Margin="10">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Left Panel - Product Selection -->
        <Grid Grid.Column="0" Margin="0,0,10,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <materialDesign:Card Grid.Row="0" Padding="15" Margin="0,0,0,10">
                <DockPanel>
                    <StackPanel DockPanel.Dock="Left">
                        <TextBlock Text="Point de Vente" FontSize="20" FontWeight="Bold"/>
                        <TextBlock x:Name="CashierTextBlock" FontSize="12"
                                 Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    </StackPanel>

                    <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                        <Button x:Name="NewSaleButton"
                              Style="{StaticResource MaterialDesignRaisedButton}"
                              Content="NOUVELLE VENTE"
                              Margin="5,0"
                              Click="NewSaleButton_Click"/>

                        <Button x:Name="HoldSaleButton"
                              Style="{StaticResource MaterialDesignOutlinedButton}"
                              Content="SUSPENDRE"
                              Margin="5,0"
                              Click="HoldSaleButton_Click"/>
                    </StackPanel>
                </DockPanel>
            </materialDesign:Card>

            <!-- Barcode Scanner -->
            <materialDesign:Card Grid.Row="1" Padding="15" Margin="0,0,0,10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBox x:Name="BarcodeTextBox" Grid.Column="0"
                           materialDesign:HintAssist.Hint="Scanner ou saisir le code-barres..."
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           FontSize="16"
                           KeyDown="BarcodeTextBox_KeyDown"
                           Margin="0,0,10,0"/>

                    <Button x:Name="SearchProductButton" Grid.Column="1"
                          Style="{StaticResource MaterialDesignIconButton}"
                          ToolTip="Rechercher Produit"
                          Click="SearchProductButton_Click"
                          Margin="5,0">
                        <materialDesign:PackIcon Kind="Magnify" Width="20" Height="20"/>
                    </Button>

                    <Button x:Name="ManualEntryButton" Grid.Column="2"
                          Style="{StaticResource MaterialDesignIconButton}"
                          ToolTip="Saisie Manuelle"
                          Click="ManualEntryButton_Click"
                          Margin="5,0">
                        <materialDesign:PackIcon Kind="Keyboard" Width="20" Height="20"/>
                    </Button>
                </Grid>
            </materialDesign:Card>

            <!-- Cart Items -->
            <materialDesign:Card Grid.Row="2" Padding="15">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <DockPanel Grid.Row="0" Margin="0,0,0,10">
                        <TextBlock Text="Articles dans le Panier" FontSize="16" FontWeight="Medium"
                                 DockPanel.Dock="Left"/>

                        <Button x:Name="ClearCartButton"
                              Style="{StaticResource MaterialDesignIconButton}"
                              ToolTip="Vider le Panier"
                              Click="ClearCartButton_Click"
                              DockPanel.Dock="Right">
                            <materialDesign:PackIcon Kind="DeleteSweep" Width="20" Height="20"/>
                        </Button>
                    </DockPanel>

                    <DataGrid x:Name="CartDataGrid" Grid.Row="1"
                            AutoGenerateColumns="False"
                            CanUserAddRows="False"
                            CanUserDeleteRows="False"
                            SelectionMode="Single"
                            GridLinesVisibility="Horizontal"
                            HeadersVisibility="Column">

                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Produit" Binding="{Binding ProductName}" Width="200"/>
                            <DataGridTextColumn Header="Prix Unit." Binding="{Binding UnitPrice, StringFormat='{}{0:N2}'}" Width="80"/>

                            <DataGridTemplateColumn Header="Qté" Width="80">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBox Text="{Binding Quantity, UpdateSourceTrigger=PropertyChanged}"
                                               TextAlignment="Center"
                                               BorderThickness="0"
                                               Background="Transparent"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <DataGridTextColumn Header="Total" Binding="{Binding LineTotal, StringFormat='{}{0:N2}'}" Width="100"/>

                            <DataGridTemplateColumn Header="" Width="40">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                              ToolTip="Supprimer"
                                              Click="RemoveItemButton_Click"
                                              Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                                        </Button>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </materialDesign:Card>
        </Grid>

        <!-- Right Panel - Invoice Summary -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Customer Selection -->
            <materialDesign:Card Grid.Row="0" Padding="15" Margin="0,0,0,10">
                <StackPanel>
                    <TextBlock Text="Client" FontSize="16" FontWeight="Medium" Margin="0,0,0,10"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <ComboBox x:Name="ClientComboBox" Grid.Column="0"
                                materialDesign:HintAssist.Hint="Sélectionner un client"
                                Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                                Margin="0,0,10,0"/>

                        <Button x:Name="AddClientButton" Grid.Column="1"
                              Style="{StaticResource MaterialDesignIconButton}"
                              ToolTip="Nouveau Client"
                              Click="AddClientButton_Click">
                            <materialDesign:PackIcon Kind="AccountPlus" Width="20" Height="20"/>
                        </Button>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Invoice Summary -->
            <materialDesign:Card Grid.Row="1" Padding="15" Margin="0,0,0,10">
                <StackPanel>
                    <TextBlock Text="Résumé de la Facture" FontSize="16" FontWeight="Medium" Margin="0,0,0,15"/>

                    <!-- Totals -->
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Sous-total HT:" FontSize="14"/>
                        <TextBlock x:Name="SubtotalHTTextBlock" Grid.Row="0" Grid.Column="1" Text="0.00 DA" FontSize="14" FontWeight="Medium"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="TVA:" FontSize="14"/>
                        <TextBlock x:Name="TVATextBlock" Grid.Row="1" Grid.Column="1" Text="0.00 DA" FontSize="14" FontWeight="Medium"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Sous-total TTC:" FontSize="14"/>
                        <TextBlock x:Name="SubtotalTTCTextBlock" Grid.Row="2" Grid.Column="1" Text="0.00 DA" FontSize="14" FontWeight="Medium"/>

                        <Separator Grid.Row="3" Grid.ColumnSpan="2" Margin="0,10"/>

                        <!-- Discount -->
                        <TextBlock Grid.Row="4" Grid.Column="0" Text="Remise:" FontSize="14"/>
                        <StackPanel Grid.Row="4" Grid.Column="1" Orientation="Horizontal">
                            <TextBox x:Name="DiscountTextBox" Width="60" Text="0" TextAlignment="Center"
                                   TextChanged="DiscountTextBox_TextChanged"/>
                            <ComboBox x:Name="DiscountTypeComboBox" Width="50" SelectedIndex="0" Margin="5,0,0,0">
                                <ComboBoxItem Content="%"/>
                                <ComboBoxItem Content="DA"/>
                            </ComboBox>
                        </StackPanel>

                        <TextBlock Grid.Row="5" Grid.Column="0" Text="Timbre:" FontSize="14"/>
                        <TextBlock x:Name="TimbreTextBlock" Grid.Row="5" Grid.Column="1" Text="0.00 DA" FontSize="14" FontWeight="Medium"/>
                    </Grid>

                    <Separator Margin="0,15"/>

                    <!-- Final Total -->
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="TOTAL À PAYER:" FontSize="18" FontWeight="Bold"/>
                        <TextBlock x:Name="FinalTotalTextBlock" Grid.Column="1" Text="0.00 DA"
                                 FontSize="18" FontWeight="Bold"
                                 Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Payment and Actions -->
            <materialDesign:Card Grid.Row="2" Padding="15">
                <StackPanel>
                    <TextBlock Text="Paiement" FontSize="16" FontWeight="Medium" Margin="0,0,0,10"/>

                    <!-- Payment Method -->
                    <ComboBox x:Name="PaymentMethodComboBox"
                            materialDesign:HintAssist.Hint="Mode de Paiement"
                            Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                            Margin="0,0,0,10"
                            SelectionChanged="PaymentMethodComboBox_SelectionChanged">
                        <ComboBoxItem Content="Espèce" IsSelected="True"/>
                        <ComboBoxItem Content="Carte Bancaire"/>
                        <ComboBoxItem Content="Chèque"/>
                        <ComboBoxItem Content="Crédit"/>
                    </ComboBox>

                    <!-- Amount Received -->
                    <TextBox x:Name="AmountReceivedTextBox"
                           materialDesign:HintAssist.Hint="Montant Reçu"
                           Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                           Margin="0,0,0,10"
                           TextChanged="AmountReceivedTextBox_TextChanged"/>

                    <!-- Change -->
                    <Grid Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="Monnaie à rendre:" FontSize="14"/>
                        <TextBlock x:Name="ChangeTextBlock" Grid.Column="1" Text="0.00 DA"
                                 FontSize="14" FontWeight="Medium"/>
                    </Grid>

                    <!-- Action Buttons -->
                    <StackPanel>
                        <Button x:Name="ProcessSaleButton"
                              Style="{StaticResource MaterialDesignRaisedButton}"
                              Content="VALIDER LA VENTE"
                              Height="45"
                              FontSize="14" FontWeight="Medium"
                              Margin="0,0,0,10"
                              Click="ProcessSaleButton_Click"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Button x:Name="PrintButton" Grid.Column="0"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Content="IMPRIMER"
                                  Margin="0,0,5,0"
                                  Click="PrintButton_Click"/>

                            <Button x:Name="EmailButton" Grid.Column="1"
                                  Style="{StaticResource MaterialDesignOutlinedButton}"
                                  Content="EMAIL"
                                  Margin="5,0,0,0"
                                  Click="EmailButton_Click"/>
                        </Grid>
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>
        </Grid>
    </Grid>
</Page>
