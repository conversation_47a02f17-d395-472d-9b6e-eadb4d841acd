<Window x:Class="GestioDeStock.Views.Dialogs.ProductDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Gestion du Produit" 
        Height="700" Width="800"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock x:Name="HeaderTextBlock" Grid.Row="0" 
                 Text="Nouveau Produit" 
                 FontSize="24" FontWeight="Bold" 
                 Margin="0,0,0,20"/>
        
        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- Basic Information -->
                <materialDesign:Card Margin="0,0,0,15" Padding="20">
                    <StackPanel>
                        <TextBlock Text="Informations de Base" FontSize="18" FontWeight="Medium" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <!-- Code -->
                            <TextBox x:Name="CodeTextBox" Grid.Row="0" Grid.Column="0"
                                   materialDesign:HintAssist.Hint="Code Produit *"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Margin="0,0,10,15"/>
                            
                            <!-- Name -->
                            <TextBox x:Name="NameTextBox" Grid.Row="0" Grid.Column="1"
                                   materialDesign:HintAssist.Hint="Nom du Produit *"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Margin="10,0,0,15"/>
                            
                            <!-- Category -->
                            <ComboBox x:Name="CategoryComboBox" Grid.Row="1" Grid.Column="0"
                                    materialDesign:HintAssist.Hint="Catégorie"
                                    Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                                    Margin="0,0,10,15"/>
                            
                            <!-- Brand -->
                            <ComboBox x:Name="BrandComboBox" Grid.Row="1" Grid.Column="1"
                                    materialDesign:HintAssist.Hint="Marque"
                                    Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                                    Margin="10,0,0,15"/>
                            
                            <!-- Unit -->
                            <ComboBox x:Name="UnitComboBox" Grid.Row="2" Grid.Column="0"
                                    materialDesign:HintAssist.Hint="Unité de Base *"
                                    Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                                    Margin="0,0,10,15"/>
                            
                            <!-- Expiry Date -->
                            <DatePicker x:Name="ExpiryDatePicker" Grid.Row="2" Grid.Column="1"
                                      materialDesign:HintAssist.Hint="Date d'Expiration"
                                      Style="{StaticResource MaterialDesignFloatingHintDatePicker}"
                                      Margin="10,0,0,15"/>
                            
                            <!-- Description -->
                            <TextBox x:Name="DescriptionTextBox" Grid.Row="3" Grid.ColumnSpan="2"
                                   materialDesign:HintAssist.Hint="Description"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   AcceptsReturn="True" TextWrapping="Wrap"
                                   Height="60" Margin="0,0,0,15"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>
                
                <!-- Pricing Information -->
                <materialDesign:Card Margin="0,0,0,15" Padding="20">
                    <StackPanel>
                        <TextBlock Text="Informations de Prix" FontSize="18" FontWeight="Medium" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <!-- Purchase Price -->
                            <TextBox x:Name="PurchasePriceTextBox" Grid.Row="0" Grid.Column="0"
                                   materialDesign:HintAssist.Hint="Prix d'Achat (DA)"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Margin="0,0,10,15"
                                   TextChanged="PriceTextBox_TextChanged"/>
                            
                            <!-- Sale Price HT -->
                            <TextBox x:Name="SalePriceHTTextBox" Grid.Row="0" Grid.Column="1"
                                   materialDesign:HintAssist.Hint="Prix de Vente HT (DA)"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Margin="5,0,5,15"
                                   TextChanged="PriceTextBox_TextChanged"/>
                            
                            <!-- Sale Price TTC -->
                            <TextBox x:Name="SalePriceTTCTextBox" Grid.Row="0" Grid.Column="2"
                                   materialDesign:HintAssist.Hint="Prix de Vente TTC (DA)"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Margin="10,0,0,15"
                                   TextChanged="PriceTextBox_TextChanged"/>
                            
                            <!-- TVA Rate -->
                            <TextBox x:Name="TVATextBox" Grid.Row="1" Grid.Column="0"
                                   materialDesign:HintAssist.Hint="Taux TVA (%)"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Text="19.00"
                                   Margin="0,0,10,15"
                                   TextChanged="PriceTextBox_TextChanged"/>
                            
                            <!-- PMP -->
                            <TextBox x:Name="PMPTextBox" Grid.Row="1" Grid.Column="1"
                                   materialDesign:HintAssist.Hint="PMP (DA)"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   IsReadOnly="True"
                                   Margin="5,0,5,15"/>
                            
                            <!-- Pieces per Fardeau -->
                            <TextBox x:Name="PiecesPerFardeauTextBox" Grid.Row="1" Grid.Column="2"
                                   materialDesign:HintAssist.Hint="Pièces par Fardeau"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Text="1"
                                   Margin="10,0,0,15"/>
                            
                            <!-- Fardeau Price -->
                            <TextBox x:Name="FardeauPriceTextBox" Grid.Row="2" Grid.Column="0"
                                   materialDesign:HintAssist.Hint="Prix Fardeau (DA)"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Margin="0,0,10,15"/>
                            
                            <!-- Min Stock Alert -->
                            <TextBox x:Name="MinStockTextBox" Grid.Row="2" Grid.Column="1"
                                   materialDesign:HintAssist.Hint="Alerte Stock Min"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Text="0"
                                   Margin="5,0,5,15"/>
                            
                            <!-- Max Stock Alert -->
                            <TextBox x:Name="MaxStockTextBox" Grid.Row="2" Grid.Column="2"
                                   materialDesign:HintAssist.Hint="Alerte Stock Max"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Margin="10,0,0,15"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>
                
                <!-- Barcode and Options -->
                <materialDesign:Card Margin="0,0,0,15" Padding="20">
                    <StackPanel>
                        <TextBlock Text="Code-barres et Options" FontSize="18" FontWeight="Medium" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <!-- Primary Barcode -->
                            <TextBox x:Name="PrimaryBarcodeTextBox" Grid.Row="0" Grid.Column="0"
                                   materialDesign:HintAssist.Hint="Code-barres Principal"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Margin="0,0,10,15"/>
                            
                            <!-- Scale Product -->
                            <CheckBox x:Name="IsScaleProductCheckBox" Grid.Row="0" Grid.Column="1"
                                    Content="Produit de Balance"
                                    Margin="10,0,0,15"
                                    Checked="IsScaleProductCheckBox_Checked"
                                    Unchecked="IsScaleProductCheckBox_Unchecked"/>
                            
                            <!-- Scale Prefix -->
                            <TextBox x:Name="ScalePrefixTextBox" Grid.Row="1" Grid.Column="0"
                                   materialDesign:HintAssist.Hint="Préfixe Balance (22/02)"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   IsEnabled="False"
                                   Margin="0,0,10,15"/>
                            
                            <!-- Status Options -->
                            <StackPanel Grid.Row="1" Grid.Column="1" Margin="10,0,0,15">
                                <CheckBox x:Name="IsActiveCheckBox" Content="Produit Actif" IsChecked="True"/>
                                <CheckBox x:Name="IsArchivedCheckBox" Content="Archivé" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="CancelButton" 
                  Content="ANNULER" 
                  Style="{StaticResource MaterialDesignFlatButton}"
                  Margin="0,0,10,0"
                  Click="CancelButton_Click"/>
            
            <Button x:Name="SaveButton" 
                  Content="ENREGISTRER" 
                  Style="{StaticResource MaterialDesignRaisedButton}"
                  Click="SaveButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
