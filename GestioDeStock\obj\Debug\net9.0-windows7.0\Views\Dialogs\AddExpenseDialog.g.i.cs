﻿#pragma checksum "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "3A04BBFC4147A0463DA8BDED41C82A525396EDAF"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GestioDeStock.Views.Dialogs {
    
    
    /// <summary>
    /// AddExpenseDialog
    /// </summary>
    public partial class AddExpenseDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 20 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.PackIcon HeaderIcon;
        
        #line default
        #line hidden
        
        
        #line 23 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderTextBlock;
        
        #line default
        #line hidden
        
        
        #line 34 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton ExpenseRadioButton;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton IncomeRadioButton;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CategoryComboBox;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AmountTextBox;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker TransactionDatePicker;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaymentMethodComboBox;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox BankAccountComboBox;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CheckDetailsGrid;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CheckNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker CheckDatePicker;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SummaryLabelTextBlock;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SummaryAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GestioDeStock;V1.0.0.0;component/views/dialogs/addexpensedialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.HeaderIcon = ((MaterialDesignThemes.Wpf.PackIcon)(target));
            return;
            case 2:
            this.HeaderTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.ExpenseRadioButton = ((System.Windows.Controls.RadioButton)(target));
            
            #line 37 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
            this.ExpenseRadioButton.Checked += new System.Windows.RoutedEventHandler(this.TypeRadioButton_Checked);
            
            #line default
            #line hidden
            return;
            case 4:
            this.IncomeRadioButton = ((System.Windows.Controls.RadioButton)(target));
            
            #line 40 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
            this.IncomeRadioButton.Checked += new System.Windows.RoutedEventHandler(this.TypeRadioButton_Checked);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CategoryComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.AmountTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 55 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
            this.AmountTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.AmountTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.TransactionDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 9:
            this.PaymentMethodComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 66 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
            this.PaymentMethodComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PaymentMethodComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.BankAccountComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.CheckDetailsGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 12:
            this.CheckNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.CheckDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 14:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.SummaryLabelTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.SummaryAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 128 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 134 "..\..\..\..\..\Views\Dialogs\AddExpenseDialog.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

