@echo off
title Gestion de Stock - Systeme de Gestion pour Epicerie
color 0A

:MENU
cls
echo.
echo ========================================
echo    GESTION DE STOCK - EPICERIE
echo ========================================
echo.
echo    Systeme de gestion complet pour
echo    magasins et epiceries
echo.
echo ========================================
echo.
echo 1. Demarrer l'application
echo 2. Mode developpement
echo 3. Construire version Release
echo 4. Mettre a jour la base de donnees
echo 5. Ouvrir le dossier du projet
echo 6. Afficher les informations
echo 7. Quitter
echo.
echo ========================================
echo.
set /p choice="Choisissez une option (1-7): "

if "%choice%"=="1" goto START_APP
if "%choice%"=="2" goto DEV_MODE
if "%choice%"=="3" goto BUILD_RELEASE
if "%choice%"=="4" goto UPDATE_DB
if "%choice%"=="5" goto OPEN_FOLDER
if "%choice%"=="6" goto SHOW_INFO
if "%choice%"=="7" goto EXIT
goto MENU

:START_APP
cls
echo ========================================
echo   Demarrage de l'application...
echo ========================================
echo.
call run.bat
pause
goto MENU

:DEV_MODE
cls
echo ========================================
echo   Mode Developpement
echo ========================================
echo.
call dev.bat
pause
goto MENU

:BUILD_RELEASE
cls
echo ========================================
echo   Construction Version Release
echo ========================================
echo.
call build-release.bat
goto MENU

:UPDATE_DB
cls
echo ========================================
echo   Mise a jour de la Base de Donnees
echo ========================================
echo.
echo ATTENTION: Cette operation va modifier votre base de donnees.
echo Assurez-vous d'avoir fait une sauvegarde avant de continuer.
echo.
set /p confirm="Continuer? (O/N): "
if /i "%confirm%"=="O" (
    echo.
    echo Execution du script de mise a jour...
    mysql -u root -p gestion_stock < update-database.sql
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo Mise a jour terminee avec succes!
    ) else (
        echo.
        echo Erreur lors de la mise a jour!
        echo Verifiez que MySQL est installe et accessible.
    )
) else (
    echo Operation annulee.
)
pause
goto MENU

:OPEN_FOLDER
cls
echo Ouverture du dossier du projet...
explorer .
goto MENU

:SHOW_INFO
cls
echo ========================================
echo   INFORMATIONS SYSTEME
echo ========================================
echo.
echo Nom du Projet    : Gestion de Stock
echo Version          : 1.0.0
echo Framework        : .NET 6.0
echo Interface        : WPF + MaterialDesign
echo Base de Donnees  : MySQL 8.0+
echo ORM              : Dapper
echo.
echo ========================================
echo   FONCTIONNALITES PRINCIPALES
echo ========================================
echo.
echo - Gestion complete des produits
echo - Point de vente avec scanner
echo - Gestion du stock multi-magasins
echo - Facturation et devis
echo - Gestion clients/fournisseurs
echo - Rapports et statistiques
echo - Codes-barres et balance
echo - Gestion des utilisateurs
echo - Sauvegarde automatique
echo.
echo ========================================
echo   DONNEES DE CONNEXION PAR DEFAUT
echo ========================================
echo.
echo Utilisateur : admin
echo Mot de passe: admin
echo.
echo IMPORTANT: Changez le mot de passe
echo           apres la premiere connexion!
echo.
echo ========================================
echo   SUPPORT TECHNIQUE
echo ========================================
echo.
echo 1. Consultez le fichier README.md
echo 2. Consultez INSTALLATION.md
echo 3. Verifiez les logs dans le dossier Logs/
echo.
pause
goto MENU

:EXIT
cls
echo.
echo Merci d'avoir utilise Gestion de Stock!
echo.
echo Developpe avec .NET 6.0 et MaterialDesign
echo Pour magasins et epiceries
echo.
timeout /t 3 /nobreak >nul
exit

:ERROR
echo.
echo Une erreur s'est produite!
pause
goto MENU
