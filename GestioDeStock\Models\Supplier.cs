using System.Collections.Generic;

namespace GestioDeStock.Models
{
    public class Supplier : BaseEntity
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? Address { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Nif { get; set; }
        public string? Nis { get; set; }
        public string? Rc { get; set; }
        public string? Article { get; set; }
        public decimal CreditLimit { get; set; } = 0.00m;
        public decimal OpeningBalance { get; set; } = 0.00m;
        public decimal CurrentBalance { get; set; } = 0.00m;
        public bool IsActive { get; set; } = true;
        public bool IsArchived { get; set; } = false;

        // Navigation properties
        public List<PurchaseInvoice> PurchaseInvoices { get; set; } = new List<PurchaseInvoice>();
    }
}
