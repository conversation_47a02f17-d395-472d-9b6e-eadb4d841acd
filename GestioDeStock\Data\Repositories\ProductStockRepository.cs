using Dapper;
using GestioDeStock.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace GestioDeStock.Data.Repositories
{
    public class ProductStockRepository
    {
        private readonly DatabaseConnection _database;

        public ProductStockRepository(DatabaseConnection database)
        {
            _database = database;
        }

        public async Task<IEnumerable<ProductStock>> GetAllAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT ps.*, p.name as ProductName, w.name as WarehouseName
                FROM product_stock ps
                LEFT JOIN products p ON ps.product_id = p.id
                LEFT JOIN warehouses w ON ps.warehouse_id = w.id
                ORDER BY p.name";
            
            return await connection.QueryAsync<ProductStock>(sql);
        }

        public async Task<ProductStock?> GetByProductAndWarehouseAsync(int productId, int warehouseId)
        {
            using var connection = _database.CreateConnection();
            var sql = "SELECT * FROM product_stock WHERE product_id = @ProductId AND warehouse_id = @WarehouseId";
            return await connection.QueryFirstOrDefaultAsync<ProductStock>(sql, new { ProductId = productId, WarehouseId = warehouseId });
        }

        public async Task<IEnumerable<ProductStock>> GetByProductAsync(int productId)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT ps.*, w.name as WarehouseName
                FROM product_stock ps
                LEFT JOIN warehouses w ON ps.warehouse_id = w.id
                WHERE ps.product_id = @ProductId
                ORDER BY w.name";
            
            return await connection.QueryAsync<ProductStock>(sql, new { ProductId = productId });
        }

        public async Task<IEnumerable<ProductStock>> GetByWarehouseAsync(int warehouseId)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT ps.*, p.name as ProductName, p.code as ProductCode
                FROM product_stock ps
                LEFT JOIN products p ON ps.product_id = p.id
                WHERE ps.warehouse_id = @WarehouseId
                ORDER BY p.name";
            
            return await connection.QueryAsync<ProductStock>(sql, new { WarehouseId = warehouseId });
        }

        public async Task<bool> UpdateStockAsync(int productId, int warehouseId, decimal quantityChange, decimal unitCost = 0)
        {
            using var connection = _database.CreateConnection();
            
            // Check if stock record exists
            var existingStock = await GetByProductAndWarehouseAsync(productId, warehouseId);
            
            if (existingStock == null)
            {
                // Create new stock record
                var insertSql = @"
                    INSERT INTO product_stock (product_id, warehouse_id, quantity_available, last_cost, average_cost, last_updated)
                    VALUES (@ProductId, @WarehouseId, @Quantity, @UnitCost, @UnitCost, NOW())";
                
                var insertResult = await connection.ExecuteAsync(insertSql, new 
                { 
                    ProductId = productId, 
                    WarehouseId = warehouseId, 
                    Quantity = Math.Max(0, quantityChange),
                    UnitCost = unitCost 
                });
                
                return insertResult > 0;
            }
            else
            {
                // Update existing stock
                var newQuantity = Math.Max(0, existingStock.QuantityAvailable + quantityChange);
                var newAverageCost = existingStock.AverageCost;
                
                // Calculate new average cost if adding stock
                if (quantityChange > 0 && unitCost > 0)
                {
                    var totalValue = (existingStock.QuantityAvailable * existingStock.AverageCost) + (quantityChange * unitCost);
                    var totalQuantity = existingStock.QuantityAvailable + quantityChange;
                    newAverageCost = totalQuantity > 0 ? totalValue / totalQuantity : unitCost;
                }
                
                var updateSql = @"
                    UPDATE product_stock 
                    SET quantity_available = @NewQuantity,
                        last_cost = @UnitCost,
                        average_cost = @AverageCost,
                        last_updated = NOW()
                    WHERE product_id = @ProductId AND warehouse_id = @WarehouseId";
                
                var updateResult = await connection.ExecuteAsync(updateSql, new 
                { 
                    NewQuantity = newQuantity,
                    UnitCost = unitCost > 0 ? unitCost : existingStock.LastCost,
                    AverageCost = newAverageCost,
                    ProductId = productId, 
                    WarehouseId = warehouseId 
                });
                
                return updateResult > 0;
            }
        }

        public async Task<bool> ReserveStockAsync(int productId, int warehouseId, decimal quantity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE product_stock 
                SET quantity_available = quantity_available - @Quantity,
                    quantity_reserved = quantity_reserved + @Quantity,
                    last_updated = NOW()
                WHERE product_id = @ProductId 
                  AND warehouse_id = @WarehouseId 
                  AND quantity_available >= @Quantity";
            
            var affectedRows = await connection.ExecuteAsync(sql, new { ProductId = productId, WarehouseId = warehouseId, Quantity = quantity });
            return affectedRows > 0;
        }

        public async Task<bool> ReleaseReservedStockAsync(int productId, int warehouseId, decimal quantity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE product_stock 
                SET quantity_available = quantity_available + @Quantity,
                    quantity_reserved = quantity_reserved - @Quantity,
                    last_updated = NOW()
                WHERE product_id = @ProductId 
                  AND warehouse_id = @WarehouseId 
                  AND quantity_reserved >= @Quantity";
            
            var affectedRows = await connection.ExecuteAsync(sql, new { ProductId = productId, WarehouseId = warehouseId, Quantity = quantity });
            return affectedRows > 0;
        }

        public async Task<IEnumerable<ProductStock>> GetLowStockProductsAsync(int? warehouseId = null)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT ps.*, p.name as ProductName, p.code as ProductCode, p.min_stock_alert, w.name as WarehouseName
                FROM product_stock ps
                INNER JOIN products p ON ps.product_id = p.id
                INNER JOIN warehouses w ON ps.warehouse_id = w.id
                WHERE ps.quantity_available <= p.min_stock_alert
                  AND p.is_active = 1";
            
            if (warehouseId.HasValue)
            {
                sql += " AND ps.warehouse_id = @WarehouseId";
            }
            
            sql += " ORDER BY p.name";
            
            return await connection.QueryAsync<ProductStock>(sql, new { WarehouseId = warehouseId });
        }

        public async Task<decimal> GetTotalStockValueAsync(int? warehouseId = null)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT COALESCE(SUM(ps.quantity_available * ps.average_cost), 0)
                FROM product_stock ps
                WHERE ps.quantity_available > 0";
            
            if (warehouseId.HasValue)
            {
                sql += " AND ps.warehouse_id = @WarehouseId";
            }
            
            return await connection.QuerySingleAsync<decimal>(sql, new { WarehouseId = warehouseId });
        }

        public async Task<int> GetLowStockCountAsync(int? warehouseId = null)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT COUNT(*)
                FROM product_stock ps
                INNER JOIN products p ON ps.product_id = p.id
                WHERE ps.quantity_available <= p.min_stock_alert
                  AND p.is_active = 1";
            
            if (warehouseId.HasValue)
            {
                sql += " AND ps.warehouse_id = @WarehouseId";
            }
            
            return await connection.QuerySingleAsync<int>(sql, new { WarehouseId = warehouseId });
        }
    }
}
