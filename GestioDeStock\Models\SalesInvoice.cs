using System;
using System.Collections.Generic;

namespace GestioDeStock.Models
{
    public class SalesInvoice : BaseEntity
    {
        public string InvoiceNumber { get; set; } = string.Empty;
        public InvoiceType InvoiceType { get; set; } = InvoiceType.Vente;
        public int? ClientId { get; set; }
        public int WarehouseId { get; set; }
        public int UserId { get; set; }
        public int? CashSessionId { get; set; }
        public DateTime InvoiceDate { get; set; } = DateTime.Now;
        public DateTime? DueDate { get; set; }
        public decimal SubtotalHt { get; set; } = 0.00m;
        public decimal TotalTva { get; set; } = 0.00m;
        public decimal TotalTtc { get; set; } = 0.00m;
        public decimal DiscountAmount { get; set; } = 0.00m;
        public decimal DiscountPercentage { get; set; } = 0.00m;
        public decimal TimbreAmount { get; set; } = 0.00m;
        public decimal FinalAmount { get; set; } = 0.00m;
        public decimal PaidAmount { get; set; } = 0.00m;
        public decimal RemainingAmount { get; set; } = 0.00m;
        public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.Espèce;
        public PaymentStatus PaymentStatus { get; set; } = PaymentStatus.NonPayé;
        public InvoiceStatus Status { get; set; } = InvoiceStatus.Brouillon;
        public string? Notes { get; set; }
        public bool IsPrinted { get; set; } = false;

        // Navigation properties
        public Client? Client { get; set; }
        public Warehouse Warehouse { get; set; } = null!;
        public User User { get; set; } = null!;
        public CashSession? CashSession { get; set; }
        public List<SalesInvoiceDetail> Details { get; set; } = new List<SalesInvoiceDetail>();
        public List<SalesPayment> Payments { get; set; } = new List<SalesPayment>();
    }

    public enum InvoiceType
    {
        Vente,
        Devis,
        Proforma,
        Retour
    }

    public enum PaymentMethod
    {
        Espèce,
        Carte,
        Chèque,
        Crédit,
        Mixte
    }

    public enum PaymentStatus
    {
        NonPayé,
        PartiellementPayé,
        Payé,
        Annulé
    }

    public enum InvoiceStatus
    {
        Brouillon,
        Confirmé,
        Livré,
        Annulé,
        Retourné
    }
}
