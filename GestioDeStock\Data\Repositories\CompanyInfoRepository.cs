using Dapper;
using GestioDeStock.Models;
using System.Threading.Tasks;

namespace GestioDeStock.Data.Repositories
{
    public class CompanyInfoRepository
    {
        private readonly DatabaseConnection _database;

        public CompanyInfoRepository(DatabaseConnection database)
        {
            _database = database;
        }

        public async Task<CompanyInfo?> GetCompanyInfoAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = "SELECT * FROM company_info LIMIT 1";
            return await connection.QueryFirstOrDefaultAsync<CompanyInfo>(sql);
        }

        public async Task<int> AddAsync(CompanyInfo entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                INSERT INTO company_info (name, address, phone, email, website, nif, nis, rc, article, 
                    logo_path, default_tva_rate, timbre_amount, timbre_threshold, currency, 
                    created_by, updated_by)
                VALUES (@Name, @Address, @Phone, @Email, @Website, @Nif, @Nis, @Rc, @Article,
                    @LogoPath, @DefaultTvaRate, @TimbreAmount, @TimbreThreshold, @Currency,
                    @CreatedBy, @UpdatedBy);
                SELECT LAST_INSERT_ID();";
            
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public async Task<bool> UpdateAsync(CompanyInfo entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE company_info SET 
                    name = @Name,
                    address = @Address,
                    phone = @Phone,
                    email = @Email,
                    website = @Website,
                    nif = @Nif,
                    nis = @Nis,
                    rc = @Rc,
                    article = @Article,
                    logo_path = @LogoPath,
                    default_tva_rate = @DefaultTvaRate,
                    timbre_amount = @TimbreAmount,
                    timbre_threshold = @TimbreThreshold,
                    currency = @Currency,
                    updated_at = NOW(),
                    updated_by = @UpdatedBy
                WHERE id = @Id";
            
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            using var connection = _database.CreateConnection();
            var sql = "DELETE FROM company_info WHERE id = @Id";
            var affectedRows = await connection.ExecuteAsync(sql, new { Id = id });
            return affectedRows > 0;
        }

        public async Task<bool> UpdateLogoAsync(int id, string logoPath)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE company_info 
                SET logo_path = @LogoPath, updated_at = NOW()
                WHERE id = @Id";
            
            var affectedRows = await connection.ExecuteAsync(sql, new { Id = id, LogoPath = logoPath });
            return affectedRows > 0;
        }

        public async Task<bool> UpdateTaxSettingsAsync(int id, decimal defaultTvaRate, decimal timbreAmount, decimal timbreThreshold)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE company_info 
                SET default_tva_rate = @DefaultTvaRate,
                    timbre_amount = @TimbreAmount,
                    timbre_threshold = @TimbreThreshold,
                    updated_at = NOW()
                WHERE id = @Id";
            
            var affectedRows = await connection.ExecuteAsync(sql, new 
            { 
                Id = id, 
                DefaultTvaRate = defaultTvaRate, 
                TimbreAmount = timbreAmount, 
                TimbreThreshold = timbreThreshold 
            });
            
            return affectedRows > 0;
        }
    }
}
