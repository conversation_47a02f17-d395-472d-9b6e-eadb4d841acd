using System.Collections.Generic;

namespace GestioDeStock.Models
{
    public class ExpenseCategory : BaseEntity
    {
        public string Name { get; set; } = string.Empty;
        public ExpenseType Type { get; set; }
        public string? Description { get; set; }
        public bool IsActive { get; set; } = true;

        // Navigation properties
        public List<Expense> Expenses { get; set; } = new List<Expense>();
    }

    public enum ExpenseType
    {
        Dépense,
        Recette
    }
}
