using Dapper;
using GestioDeStock.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace GestioDeStock.Data.Repositories
{
    public class WarehouseRepository : BaseRepository<Warehouse>
    {
        public WarehouseRepository(DatabaseConnection database) : base(database, "warehouses")
        {
        }

        public override async Task<int> AddAsync(Warehouse entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                INSERT INTO warehouses (name, address, phone, manager_name, is_active, is_main, created_by, updated_by)
                VALUES (@Name, @Address, @Phone, @ManagerName, @IsActive, @IsMain, @CreatedBy, @UpdatedBy);
                SELECT LAST_INSERT_ID();";
            
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Warehouse entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE warehouses SET 
                    name = @Name,
                    address = @Address,
                    phone = @Phone,
                    manager_name = @ManagerName,
                    is_active = @IsActive,
                    is_main = @IsMain,
                    updated_at = NOW(),
                    updated_by = @UpdatedBy
                WHERE id = @Id";
            
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public override async Task<IEnumerable<Warehouse>> SearchAsync(string searchTerm)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT * FROM warehouses 
                WHERE name LIKE @SearchTerm 
                   OR address LIKE @SearchTerm 
                   OR manager_name LIKE @SearchTerm
                ORDER BY name";
            
            return await connection.QueryAsync<Warehouse>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<IEnumerable<Warehouse>> GetActiveWarehousesAsync()
        {
            return await GetActiveAsync();
        }

        public async Task<Warehouse?> GetMainWarehouseAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = "SELECT * FROM warehouses WHERE is_main = 1 AND is_active = 1 LIMIT 1";
            return await connection.QueryFirstOrDefaultAsync<Warehouse>(sql);
        }

        public async Task<bool> SetMainWarehouseAsync(int warehouseId)
        {
            using var connection = _database.CreateConnection();
            using var transaction = connection.BeginTransaction();
            
            try
            {
                // Remove main flag from all warehouses
                await connection.ExecuteAsync("UPDATE warehouses SET is_main = 0", transaction: transaction);
                
                // Set new main warehouse
                var affectedRows = await connection.ExecuteAsync(
                    "UPDATE warehouses SET is_main = 1 WHERE id = @Id", 
                    new { Id = warehouseId }, 
                    transaction: transaction);
                
                transaction.Commit();
                return affectedRows > 0;
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }
    }
}
