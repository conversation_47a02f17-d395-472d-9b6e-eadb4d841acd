using Dapper;
using GestioDeStock.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace GestioDeStock.Data.Repositories
{
    public class ExpenseCategoryRepository : BaseRepository<ExpenseCategory>
    {
        public ExpenseCategoryRepository(DatabaseConnection database) : base(database, "expense_categories")
        {
        }

        public override async Task<int> AddAsync(ExpenseCategory entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                INSERT INTO expense_categories (name, type, description, is_active, created_by, updated_by)
                VALUES (@Name, @Type, @Description, @IsActive, @CreatedBy, @UpdatedBy);
                SELECT LAST_INSERT_ID();";
            
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(ExpenseCategory entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE expense_categories SET 
                    name = @Name,
                    type = @Type,
                    description = @Description,
                    is_active = @IsActive,
                    updated_at = NOW(),
                    updated_by = @UpdatedBy
                WHERE id = @Id";
            
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public override async Task<IEnumerable<ExpenseCategory>> SearchAsync(string searchTerm)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT * FROM expense_categories 
                WHERE name LIKE @SearchTerm 
                   OR description LIKE @SearchTerm
                ORDER BY name";
            
            return await connection.QueryAsync<ExpenseCategory>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<IEnumerable<ExpenseCategory>> GetActiveCategoriesAsync()
        {
            return await GetActiveAsync();
        }

        public async Task<IEnumerable<ExpenseCategory>> GetCategoriesByTypeAsync(ExpenseType type)
        {
            using var connection = _database.CreateConnection();
            var sql = "SELECT * FROM expense_categories WHERE type = @Type AND is_active = 1 ORDER BY name";
            return await connection.QueryAsync<ExpenseCategory>(sql, new { Type = type.ToString() });
        }

        public async Task<ExpenseCategory?> GetByNameAsync(string name)
        {
            using var connection = _database.CreateConnection();
            var sql = "SELECT * FROM expense_categories WHERE name = @Name";
            return await connection.QueryFirstOrDefaultAsync<ExpenseCategory>(sql, new { Name = name });
        }
    }
}
