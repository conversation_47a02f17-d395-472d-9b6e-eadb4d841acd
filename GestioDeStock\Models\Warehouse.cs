using System.Collections.Generic;

namespace GestioDeStock.Models
{
    public class Warehouse : BaseEntity
    {
        public string Name { get; set; } = string.Empty;
        public string? Address { get; set; }
        public string? Phone { get; set; }
        public string? ManagerName { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsMain { get; set; } = false;

        // Navigation properties
        public List<ProductStock> ProductStocks { get; set; } = new List<ProductStock>();
        public List<SalesInvoice> SalesInvoices { get; set; } = new List<SalesInvoice>();
        public List<PurchaseInvoice> PurchaseInvoices { get; set; } = new List<PurchaseInvoice>();
        public List<CashSession> CashSessions { get; set; } = new List<CashSession>();
    }
}
