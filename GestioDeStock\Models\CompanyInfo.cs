using System;
using System.ComponentModel.DataAnnotations;

namespace GestioDeStock.Models
{
    public class CompanyInfo : BaseEntity
    {
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Address { get; set; }

        [StringLength(20)]
        public string? Phone { get; set; }

        [StringLength(100)]
        [EmailAddress]
        public string? Email { get; set; }

        [StringLength(200)]
        public string? Website { get; set; }

        [StringLength(50)]
        public string? Nif { get; set; }

        [StringLength(50)]
        public string? Nis { get; set; }

        [StringLength(50)]
        public string? Rc { get; set; }

        [StringLength(50)]
        public string? Article { get; set; }

        [StringLength(500)]
        public string? LogoPath { get; set; }

        [Range(0, 100)]
        public decimal DefaultTvaRate { get; set; } = 19.0m;

        [Range(0, double.MaxValue)]
        public decimal TimbreAmount { get; set; } = 0.0m;

        [Range(0, double.MaxValue)]
        public decimal TimbreThreshold { get; set; } = 1000.0m;

        [StringLength(10)]
        public string Currency { get; set; } = "DA";

        [StringLength(1000)]
        public string? Notes { get; set; }
    }
}
