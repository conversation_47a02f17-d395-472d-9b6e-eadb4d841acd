<Window x:Class="GestioDeStock.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Connexion - Gestion de Stock" 
        Height="500" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="20,20,20,10" >
            <StackPanel Orientation="Vertical" Margin="20">
                <materialDesign:PackIcon Kind="Store" Width="64" Height="64" 
                                       HorizontalAlignment="Center"
                                       Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                <TextBlock Text="Gestion de Stock" 
                         FontSize="24" FontWeight="Bold"
                         HorizontalAlignment="Center" Margin="0,10,0,0"/>
                <TextBlock Text="Système de gestion pour épicerie" 
                         FontSize="14" 
                         HorizontalAlignment="Center" Margin="0,5,0,0"
                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            </StackPanel>
        </materialDesign:Card>
        
        <!-- Login Form -->
        <materialDesign:Card Grid.Row="1" Margin="20,10,20,10" >
            <StackPanel Margin="30">
                <TextBlock Text="Connexion" FontSize="20" FontWeight="Medium" 
                         HorizontalAlignment="Center" Margin="0,0,0,30"/>
                
                <!-- Username -->
                <TextBox x:Name="UsernameTextBox" 
                       materialDesign:HintAssist.Hint="Nom d'utilisateur"
                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                       Margin="0,0,0,20"
                       FontSize="16"/>
                
                <!-- Password -->
                <PasswordBox x:Name="PasswordBox" 
                           materialDesign:HintAssist.Hint="Mot de passe"
                           Style="{StaticResource MaterialDesignFloatingHintPasswordBox}"
                           Margin="0,0,0,20"
                           FontSize="16"/>
                
                <!-- Remember Me -->
                <CheckBox x:Name="RememberMeCheckBox" 
                        Content="Se souvenir de moi" 
                        Margin="0,0,0,20"/>
                
                <!-- Login Button -->
                <Button x:Name="LoginButton" 
                      Content="SE CONNECTER" 
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Height="40" 
                      FontSize="14" FontWeight="Medium"
                      Click="LoginButton_Click"
                      IsDefault="True"/>
                
                <!-- Error Message -->
                <TextBlock x:Name="ErrorTextBlock" 
                         Foreground="Red" 
                         HorizontalAlignment="Center" 
                         Margin="0,15,0,0"
                         Visibility="Collapsed"/>
                
            </StackPanel>
        </materialDesign:Card>
        
        <!-- Footer -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                  HorizontalAlignment="Center" Margin="20">
            <TextBlock Text="© 2024 Mon Épicerie - Version 1.0" 
                     FontSize="12" 
                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
        </StackPanel>
        
    </Grid>
</Window>
