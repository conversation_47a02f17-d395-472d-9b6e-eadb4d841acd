<Page x:Class="GestioDeStock.Views.Pages.ProductsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="Gestion des Produits">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Style="{StaticResource CardStyle}">
            <DockPanel>
                <StackPanel DockPanel.Dock="Left">
                    <TextBlock Text="Gestion des Produits" FontSize="24" FontWeight="Bold"/>
                    <TextBlock Text="Gérer les produits, prix, stock et codes-barres" FontSize="14"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>

                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="AddProductButton"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Content="NOUVEAU PRODUIT"
                          Margin="10,0,0,0"
                          Click="AddProductButton_Click">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Plus" Width="16" Height="16"
                                                           VerticalAlignment="Center" Margin="0,0,5,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>
                </StackPanel>
            </DockPanel>
        </materialDesign:Card>

        <!-- Search and Filters -->
        <materialDesign:Card Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Search Box -->
                <TextBox x:Name="SearchTextBox" Grid.Column="0"
                       materialDesign:HintAssist.Hint="Rechercher par nom, code ou code-barres..."
                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                       Margin="0,0,10,0"
                       TextChanged="SearchTextBox_TextChanged">
                    <TextBox.InputBindings>
                        <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                    </TextBox.InputBindings>
                </TextBox>

                <!-- Category Filter -->
                <ComboBox x:Name="CategoryComboBox" Grid.Column="1"
                        materialDesign:HintAssist.Hint="Catégorie"
                        Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                        Margin="0,0,10,0"
                        SelectionChanged="CategoryComboBox_SelectionChanged"/>

                <!-- Status Filter -->
                <ComboBox x:Name="StatusComboBox" Grid.Column="2"
                        materialDesign:HintAssist.Hint="Statut"
                        Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                        Margin="0,0,10,0"
                        SelectionChanged="StatusComboBox_SelectionChanged">
                    <ComboBoxItem Content="Tous"/>
                    <ComboBoxItem Content="Actifs"/>
                    <ComboBoxItem Content="Inactifs"/>
                    <ComboBoxItem Content="Archivés"/>
                </ComboBox>

                <!-- Refresh Button -->
                <Button x:Name="RefreshButton" Grid.Column="3"
                      Style="{StaticResource MaterialDesignIconButton}"
                      ToolTip="Actualiser"
                      Click="RefreshButton_Click">
                    <materialDesign:PackIcon Kind="Refresh"/>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Products DataGrid -->
        <materialDesign:Card Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Results Header -->
                <DockPanel Grid.Row="0" Margin="0,0,0,10">
                    <TextBlock x:Name="ResultsCountTextBlock" Text="0 produits trouvés"
                             FontWeight="Medium" DockPanel.Dock="Left"/>

                    <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                        <Button x:Name="ExportButton"
                              Style="{StaticResource MaterialDesignOutlinedButton}"
                              Content="EXPORTER" Margin="5,0"
                              Click="ExportButton_Click">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FileExport" Width="16" Height="16"
                                                               VerticalAlignment="Center" Margin="0,0,5,0"/>
                                        <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <Button x:Name="PrintButton"
                              Style="{StaticResource MaterialDesignOutlinedButton}"
                              Content="IMPRIMER" Margin="5,0"
                              Click="PrintButton_Click">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Printer" Width="16" Height="16"
                                                               VerticalAlignment="Center" Margin="0,0,5,0"/>
                                        <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>
                    </StackPanel>
                </DockPanel>

                <!-- DataGrid -->
                <DataGrid x:Name="ProductsDataGrid" Grid.Row="1"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        SelectionMode="Single"
                        MouseDoubleClick="ProductsDataGrid_MouseDoubleClick">

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Code" Binding="{Binding Code}" Width="100"/>
                        <DataGridTextColumn Header="Nom du Produit" Binding="{Binding Name}" Width="200"/>
                        <DataGridTextColumn Header="Catégorie" Binding="{Binding CategoryName}" Width="120"/>
                        <DataGridTextColumn Header="Marque" Binding="{Binding BrandName}" Width="100"/>
                        <DataGridTextColumn Header="Prix Achat" Binding="{Binding PrixAchat, StringFormat='{}{0:N2} DA'}" Width="100"/>
                        <DataGridTextColumn Header="Prix Vente TTC" Binding="{Binding PrixVenteTtc, StringFormat='{}{0:N2} DA'}" Width="120"/>
                        <DataGridTextColumn Header="Stock" Binding="{Binding CurrentStock, StringFormat='{}{0:N2}'}" Width="80"/>
                        <DataGridTextColumn Header="Unité" Binding="{Binding UnitName}" Width="80"/>

                        <DataGridTemplateColumn Header="Statut" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="10" Padding="5,2" HorizontalAlignment="Center">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsActive}" Value="True">
                                                        <Setter Property="Background" Value="#4CAF50"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding IsActive}" Value="False">
                                                        <Setter Property="Background" Value="#F44336"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock Text="{Binding IsActive, Converter={StaticResource BoolToStatusConverter}}"
                                                 Foreground="White" FontSize="10" FontWeight="Bold"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="Actions" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                              ToolTip="Modifier"
                                              Click="EditButton_Click"
                                              Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="Pencil" Width="16" Height="16"/>
                                        </Button>

                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                              ToolTip="Supprimer"
                                              Click="DeleteButton_Click"
                                              Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                        </Button>

                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                              ToolTip="Code-barres"
                                              Click="BarcodeButton_Click"
                                              Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="Barcode" Width="16" Height="16"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Pagination -->
                <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                    <Button x:Name="PreviousPageButton"
                          Style="{StaticResource MaterialDesignIconButton}"
                          IsEnabled="False"
                          Click="PreviousPageButton_Click">
                        <materialDesign:PackIcon Kind="ChevronLeft"/>
                    </Button>

                    <TextBlock x:Name="PageInfoTextBlock" Text="Page 1 sur 1"
                             VerticalAlignment="Center" Margin="10,0"/>

                    <Button x:Name="NextPageButton"
                          Style="{StaticResource MaterialDesignIconButton}"
                          IsEnabled="False"
                          Click="NextPageButton_Click">
                        <materialDesign:PackIcon Kind="ChevronRight"/>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Page>
