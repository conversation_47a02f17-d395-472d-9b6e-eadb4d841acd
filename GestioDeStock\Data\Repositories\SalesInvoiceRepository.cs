using Dapper;
using GestioDeStock.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace GestioDeStock.Data.Repositories
{
    public class SalesInvoiceRepository : BaseRepository<SalesInvoice>
    {
        public SalesInvoiceRepository(DatabaseConnection database) : base(database, "sales_invoices")
        {
        }

        public override async Task<int> AddAsync(SalesInvoice entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                INSERT INTO sales_invoices (invoice_number, invoice_type, client_id, warehouse_id, user_id, 
                    cash_session_id, invoice_date, due_date, subtotal_ht, total_tva, total_ttc, 
                    discount_amount, discount_percentage, timbre_amount, final_amount, paid_amount, 
                    remaining_amount, payment_method, payment_status, status, notes, is_printed, 
                    created_by, updated_by)
                VALUES (@InvoiceNumber, @InvoiceType, @ClientId, @WarehouseId, @UserId, 
                    @CashSessionId, @InvoiceDate, @DueDate, @SubtotalHt, @TotalTva, @TotalTtc,
                    @DiscountAmount, @DiscountPercentage, @TimbreAmount, @FinalAmount, @PaidAmount,
                    @RemainingAmount, @PaymentMethod, @PaymentStatus, @Status, @Notes, @IsPrinted,
                    @CreatedBy, @UpdatedBy);
                SELECT LAST_INSERT_ID();";
            
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(SalesInvoice entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE sales_invoices SET 
                    invoice_number = @InvoiceNumber,
                    invoice_type = @InvoiceType,
                    client_id = @ClientId,
                    warehouse_id = @WarehouseId,
                    user_id = @UserId,
                    cash_session_id = @CashSessionId,
                    invoice_date = @InvoiceDate,
                    due_date = @DueDate,
                    subtotal_ht = @SubtotalHt,
                    total_tva = @TotalTva,
                    total_ttc = @TotalTtc,
                    discount_amount = @DiscountAmount,
                    discount_percentage = @DiscountPercentage,
                    timbre_amount = @TimbreAmount,
                    final_amount = @FinalAmount,
                    paid_amount = @PaidAmount,
                    remaining_amount = @RemainingAmount,
                    payment_method = @PaymentMethod,
                    payment_status = @PaymentStatus,
                    status = @Status,
                    notes = @Notes,
                    is_printed = @IsPrinted,
                    updated_at = NOW(),
                    updated_by = @UpdatedBy
                WHERE id = @Id";
            
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public override async Task<IEnumerable<SalesInvoice>> SearchAsync(string searchTerm)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT si.*, c.name as ClientName, u.full_name as UserName
                FROM sales_invoices si
                LEFT JOIN clients c ON si.client_id = c.id
                LEFT JOIN users u ON si.user_id = u.id
                WHERE si.invoice_number LIKE @SearchTerm 
                   OR c.name LIKE @SearchTerm
                ORDER BY si.invoice_date DESC";
            
            return await connection.QueryAsync<SalesInvoice>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<string> GetNextInvoiceNumberAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT CONCAT('VT', LPAD(COALESCE(MAX(CAST(SUBSTRING(invoice_number, 3) AS UNSIGNED)), 0) + 1, 6, '0'))
                FROM sales_invoices 
                WHERE invoice_number LIKE 'VT%'";
            
            return await connection.QuerySingleAsync<string>(sql);
        }

        public async Task<IEnumerable<SalesInvoice>> GetTodaysSalesAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT * FROM sales_invoices 
                WHERE DATE(invoice_date) = CURDATE() 
                ORDER BY invoice_date DESC";
            
            return await connection.QueryAsync<SalesInvoice>(sql);
        }

        public async Task<decimal> GetTotalSalesAmountAsync(DateTime fromDate, DateTime toDate)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT COALESCE(SUM(final_amount), 0) 
                FROM sales_invoices 
                WHERE invoice_date BETWEEN @FromDate AND @ToDate 
                  AND status != 'Annulé'";
            
            return await connection.QuerySingleAsync<decimal>(sql, new { FromDate = fromDate, ToDate = toDate });
        }
    }
}
