<Window x:Class="GestioDeStock.Views.Dialogs.ManualEntryDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Saisie Manuelle" 
        Height="400" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Saisie Manuelle de Produit" FontSize="20" FontWeight="Bold" Margin="0,0,0,20"/>
        
        <!-- Form -->
        <StackPanel Grid.Row="1" Spacing="15">
            <ComboBox x:Name="ProductComboBox"
                    materialDesign:HintAssist.Hint="Sélectionner un produit"
                    Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                    SelectionChanged="ProductComboBox_SelectionChanged"/>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <TextBox x:Name="QuantityTextBox" Grid.Column="0"
                       materialDesign:HintAssist.Hint="Quantité"
                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                       Margin="0,0,10,0"
                       Text="1"
                       TextChanged="QuantityTextBox_TextChanged"/>
                
                <TextBox x:Name="UnitPriceTextBox" Grid.Column="1"
                       materialDesign:HintAssist.Hint="Prix unitaire (DA)"
                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                       Margin="10,0,0,0"
                       TextChanged="UnitPriceTextBox_TextChanged"/>
            </Grid>
            
            <TextBox x:Name="DiscountTextBox"
                   materialDesign:HintAssist.Hint="Remise (%)"
                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                   Text="0"
                   TextChanged="DiscountTextBox_TextChanged"/>
            
            <Separator/>
            
            <!-- Summary -->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Grid.Column="0" Text="Sous-total:" FontSize="14"/>
                <TextBlock x:Name="SubtotalTextBlock" Grid.Row="0" Grid.Column="1" Text="0.00 DA" FontSize="14" FontWeight="Bold"/>
                
                <TextBlock Grid.Row="1" Grid.Column="0" Text="Remise:" FontSize="14"/>
                <TextBlock x:Name="DiscountAmountTextBlock" Grid.Row="1" Grid.Column="1" Text="0.00 DA" FontSize="14" FontWeight="Bold"/>
                
                <TextBlock Grid.Row="2" Grid.Column="0" Text="TVA:" FontSize="14"/>
                <TextBlock x:Name="TvaTextBlock" Grid.Row="2" Grid.Column="1" Text="0.00 DA" FontSize="14" FontWeight="Bold"/>
                
                <Separator Grid.Row="3" Grid.ColumnSpan="2" Margin="0,5"/>
                
                <TextBlock Grid.Row="4" Grid.Column="0" Text="Total TTC:" FontSize="16" FontWeight="Bold"/>
                <TextBlock x:Name="TotalTextBlock" Grid.Row="4" Grid.Column="1" Text="0.00 DA" FontSize="16" FontWeight="Bold" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
            </Grid>
        </StackPanel>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="AddButton" 
                  Style="{StaticResource MaterialDesignRaisedButton}"
                  Content="AJOUTER" 
                  Margin="0,0,10,0"
                  Click="AddButton_Click"
                  IsEnabled="False"/>
            
            <Button x:Name="CancelButton" 
                  Style="{StaticResource MaterialDesignOutlinedButton}"
                  Content="ANNULER" 
                  Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
