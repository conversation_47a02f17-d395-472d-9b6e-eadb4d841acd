using Microsoft.Extensions.Configuration;
using System.IO;

namespace GestioDeStock.Services
{
    public class ConfigurationService
    {
        private readonly IConfiguration _configuration;
        private static ConfigurationService? _instance;
        private static readonly object _lock = new object();

        private ConfigurationService()
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ENVIRONMENT") ?? "Production"}.json", optional: true);

            _configuration = builder.Build();
        }

        public static ConfigurationService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new ConfigurationService();
                    }
                }
                return _instance;
            }
        }

        // Connection String
        public string GetConnectionString(string name = "DefaultConnection")
        {
            return _configuration.GetConnectionString(name) ?? 
                   "Server=localhost;Database=gestion_stock;Uid=root;Pwd=;CharSet=utf8mb4;";
        }

        // App Settings
        public string CompanyName => _configuration["AppSettings:CompanyName"] ?? "Mon Épicerie";
        public string ApplicationVersion => _configuration["AppSettings:ApplicationVersion"] ?? "1.0.0";
        public string DefaultLanguage => _configuration["AppSettings:DefaultLanguage"] ?? "fr-FR";
        public string DefaultCurrency => _configuration["AppSettings:DefaultCurrency"] ?? "DA";
        public decimal DefaultTVARate => decimal.Parse(_configuration["AppSettings:DefaultTVARate"] ?? "19.00");
        public string BackupPath => _configuration["AppSettings:BackupPath"] ?? @"C:\Backups\GestionStock";
        public string ReportsPath => _configuration["AppSettings:ReportsPath"] ?? @"C:\Reports\GestionStock";
        public int MaxLoginAttempts => int.Parse(_configuration["AppSettings:MaxLoginAttempts"] ?? "3");
        public int SessionTimeoutMinutes => int.Parse(_configuration["AppSettings:SessionTimeoutMinutes"] ?? "60");
        public bool AutoBackupEnabled => bool.Parse(_configuration["AppSettings:AutoBackupEnabled"] ?? "true");
        public int AutoBackupIntervalHours => int.Parse(_configuration["AppSettings:AutoBackupIntervalHours"] ?? "24");

        // Printer Settings
        public bool ThermalPrinterEnabled => bool.Parse(_configuration["PrinterSettings:ThermalPrinterEnabled"] ?? "false");
        public string DefaultPrinterName => _configuration["PrinterSettings:DefaultPrinterName"] ?? "";
        public int PaperWidth => int.Parse(_configuration["PrinterSettings:PaperWidth"] ?? "80");
        public bool PrintLogo => bool.Parse(_configuration["PrinterSettings:PrintLogo"] ?? "true");
        public bool PrintFooter => bool.Parse(_configuration["PrinterSettings:PrintFooter"] ?? "true");

        // Barcode Settings
        public bool ScaleBarcodeEnabled => bool.Parse(_configuration["BarcodeSettings:ScaleBarcodeEnabled"] ?? "true");
        public string WeightPrefix => _configuration["BarcodeSettings:WeightPrefix"] ?? "22";
        public string PricePrefix => _configuration["BarcodeSettings:PricePrefix"] ?? "02";
        public int ProductCodeLength => int.Parse(_configuration["BarcodeSettings:ProductCodeLength"] ?? "4");
        public int WeightPrecision => int.Parse(_configuration["BarcodeSettings:WeightPrecision"] ?? "3");
        public int PricePrecision => int.Parse(_configuration["BarcodeSettings:PricePrecision"] ?? "2");

        // Security Settings
        public int PasswordMinLength => int.Parse(_configuration["SecuritySettings:PasswordMinLength"] ?? "6");
        public bool RequireUppercase => bool.Parse(_configuration["SecuritySettings:RequireUppercase"] ?? "false");
        public bool RequireNumbers => bool.Parse(_configuration["SecuritySettings:RequireNumbers"] ?? "false");
        public bool RequireSpecialChars => bool.Parse(_configuration["SecuritySettings:RequireSpecialChars"] ?? "false");
        public int PasswordExpiryDays => int.Parse(_configuration["SecuritySettings:PasswordExpiryDays"] ?? "0");
        public bool EnableAuditLog => bool.Parse(_configuration["SecuritySettings:EnableAuditLog"] ?? "true");

        // UI Settings
        public string Theme => _configuration["UISettings:Theme"] ?? "Light";
        public string PrimaryColor => _configuration["UISettings:PrimaryColor"] ?? "Blue";
        public string SecondaryColor => _configuration["UISettings:SecondaryColor"] ?? "Orange";
        public string FontSize => _configuration["UISettings:FontSize"] ?? "Medium";
        public bool ShowAnimations => bool.Parse(_configuration["UISettings:ShowAnimations"] ?? "true");
        public int AutoRefreshInterval => int.Parse(_configuration["UISettings:AutoRefreshInterval"] ?? "30");

        // Business Rules
        public bool AllowNegativeStock => bool.Parse(_configuration["BusinessRules:AllowNegativeStock"] ?? "false");
        public bool RequireCustomerForSale => bool.Parse(_configuration["BusinessRules:RequireCustomerForSale"] ?? "false");
        public bool AutoCalculatePMP => bool.Parse(_configuration["BusinessRules:AutoCalculatePMP"] ?? "true");
        public string DefaultStockMethod => _configuration["BusinessRules:DefaultStockMethod"] ?? "FIFO";
        public decimal MinimumSaleAmount => decimal.Parse(_configuration["BusinessRules:MinimumSaleAmount"] ?? "0.01");
        public decimal MaximumDiscountPercent => decimal.Parse(_configuration["BusinessRules:MaximumDiscountPercent"] ?? "50.0");

        // Timbre Settings
        public bool EnableTimbre => bool.Parse(_configuration["TimbreSettings:EnableTimbre"] ?? "true");

        public decimal GetTimbreRate(decimal amount)
        {
            if (!EnableTimbre) return 0;

            if (amount <= 300)
                return 0;
            else if (amount <= 30000)
                return 1;
            else if (amount <= 100000)
                return Math.Ceiling(amount / 100) * 1.5m;
            else
                return Math.Ceiling(amount / 100) * 1.5m;
        }

        // Logging Settings
        public bool EnableFileLogging => bool.Parse(_configuration["Logging:EnableFileLogging"] ?? "true");
        public string LogFilePath => _configuration["Logging:LogFilePath"] ?? @"Logs\app.log";
        public int MaxLogFileSizeMB => int.Parse(_configuration["Logging:MaxLogFileSizeMB"] ?? "10");
        public int MaxLogFiles => int.Parse(_configuration["Logging:MaxLogFiles"] ?? "5");

        // Helper methods
        public void EnsureDirectoriesExist()
        {
            try
            {
                if (!Directory.Exists(BackupPath))
                    Directory.CreateDirectory(BackupPath);

                if (!Directory.Exists(ReportsPath))
                    Directory.CreateDirectory(ReportsPath);

                if (EnableFileLogging)
                {
                    var logDir = Path.GetDirectoryName(LogFilePath);
                    if (!string.IsNullOrEmpty(logDir) && !Directory.Exists(logDir))
                        Directory.CreateDirectory(logDir);
                }
            }
            catch (Exception ex)
            {
                // Log error or handle appropriately
                System.Diagnostics.Debug.WriteLine($"Error creating directories: {ex.Message}");
            }
        }

        public T GetValue<T>(string key, T defaultValue = default!)
        {
            try
            {
                var value = _configuration[key];
                if (string.IsNullOrEmpty(value))
                    return defaultValue;

                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }
    }
}
