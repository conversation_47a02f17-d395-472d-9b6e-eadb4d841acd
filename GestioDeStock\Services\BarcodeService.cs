using GestioDeStock.Models;
using System.Text.RegularExpressions;

namespace GestioDeStock.Services
{
    public class BarcodeService
    {
        // Structure du code-barres de balance: PPCCCCQQQQQC
        // PP = Préfixe (22 ou 02)
        // CCCC = Code Produit
        // QQQQQ = Poids (en grammes) ou Prix (en centimes)
        // C = Check digit (optionnel)

        public class ScaleBarcodeInfo
        {
            public bool IsScaleBarcode { get; set; }
            public string ProductCode { get; set; } = string.Empty;
            public decimal Weight { get; set; }
            public decimal Price { get; set; }
            public bool IsWeightBased { get; set; }
        }

        public ScaleBarcodeInfo ParseScaleBarcode(string barcode)
        {
            var result = new ScaleBarcodeInfo();

            // Vérifier si c'est un code-barres de balance (commence par 22 ou 02)
            if (string.IsNullOrEmpty(barcode) || barcode.Length < 12)
            {
                return result;
            }

            var prefix = barcode.Substring(0, 2);
            if (prefix != "22" && prefix != "02")
            {
                return result;
            }

            result.IsScaleBarcode = true;

            try
            {
                // Extraire le code produit (positions 2-5)
                result.ProductCode = barcode.Substring(2, 4);

                // Extraire la quantité/prix (positions 6-10)
                var quantityStr = barcode.Substring(6, 5);
                var quantity = int.Parse(quantityStr);

                // Déterminer si c'est basé sur le poids ou le prix
                // Convention: préfixe 22 = poids, préfixe 02 = prix
                result.IsWeightBased = prefix == "22";

                if (result.IsWeightBased)
                {
                    // Convertir grammes en kilogrammes
                    result.Weight = quantity / 1000.0m;
                }
                else
                {
                    // Convertir centimes en dinars
                    result.Price = quantity / 100.0m;
                }
            }
            catch
            {
                result.IsScaleBarcode = false;
            }

            return result;
        }

        public string GenerateScaleBarcode(string productCode, decimal weight, bool isWeightBased = true)
        {
            if (productCode.Length != 4)
            {
                throw new ArgumentException("Le code produit doit faire 4 caractères");
            }

            var prefix = isWeightBased ? "22" : "02";
            var quantity = isWeightBased ? (int)(weight * 1000) : (int)(weight * 100);
            var quantityStr = quantity.ToString().PadLeft(5, '0');

            // Générer le code-barres sans check digit pour l'instant
            var barcode = $"{prefix}{productCode}{quantityStr}";

            // Ajouter un check digit simple (somme modulo 10)
            var checkDigit = CalculateCheckDigit(barcode);
            
            return $"{barcode}{checkDigit}";
        }

        private int CalculateCheckDigit(string barcode)
        {
            var sum = 0;
            for (int i = 0; i < barcode.Length; i++)
            {
                var digit = int.Parse(barcode[i].ToString());
                sum += (i % 2 == 0) ? digit : digit * 3;
            }
            return (10 - (sum % 10)) % 10;
        }

        public bool ValidateBarcode(string barcode)
        {
            if (string.IsNullOrEmpty(barcode))
                return false;

            // Vérifier les codes-barres standards (EAN-13, EAN-8, UPC, etc.)
            if (Regex.IsMatch(barcode, @"^\d{8}$|^\d{12,13}$"))
                return true;

            // Vérifier les codes-barres de balance
            var scaleInfo = ParseScaleBarcode(barcode);
            return scaleInfo.IsScaleBarcode;
        }

        public decimal CalculateTimbre(decimal amount, PaymentMethod paymentMethod)
        {
            // Calcul du timbre selon la réglementation algérienne
            if (paymentMethod != PaymentMethod.Espèce)
                return 0;

            if (amount <= 300)
                return 0;
            else if (amount <= 30000)
                return 1;
            else if (amount <= 100000)
                return Math.Ceiling(amount / 100) * 1.5m;
            else
                return Math.Ceiling(amount / 100) * 1.5m;
        }
    }
}
