using System;
using System.Collections.Generic;

namespace GestioDeStock.Models
{
    public class Product : BaseEntity
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int? CategoryId { get; set; }
        public int? BrandId { get; set; }
        public int UnitId { get; set; }
        public byte[]? Image { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public decimal PrixAchat { get; set; } = 0.00m;
        public decimal PrixVenteHt { get; set; } = 0.00m;
        public decimal PrixVenteTtc { get; set; } = 0.00m;
        public decimal TvaRate { get; set; } = 19.00m;
        public decimal Pmp { get; set; } = 0.00m;
        public int? PiecesPerFardeau { get; set; } = 1;
        public decimal? PrixFardeau { get; set; }
        public decimal MinStockAlert { get; set; } = 0.00m;
        public decimal? MaxStockAlert { get; set; }
        public bool IsActive { get; set; } = true;
        public bool IsArchived { get; set; } = false;
        public bool IsScaleProduct { get; set; } = false;
        public string? ScalePrefix { get; set; }

        // Navigation properties
        public Category? Category { get; set; }
        public Brand? Brand { get; set; }
        public Unit Unit { get; set; } = null!;
        public List<ProductBarcode> Barcodes { get; set; } = new List<ProductBarcode>();
        public List<ProductUnit> ProductUnits { get; set; } = new List<ProductUnit>();
        public List<ProductStock> ProductStocks { get; set; } = new List<ProductStock>();
        public List<SalesInvoiceDetail> SalesInvoiceDetails { get; set; } = new List<SalesInvoiceDetail>();
        public List<PurchaseInvoiceDetail> PurchaseInvoiceDetails { get; set; } = new List<PurchaseInvoiceDetail>();
        public List<StockMovement> StockMovements { get; set; } = new List<StockMovement>();
        public List<PromotionProduct> PromotionProducts { get; set; } = new List<PromotionProduct>();
    }
}
