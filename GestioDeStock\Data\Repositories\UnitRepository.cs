using Dapper;
using GestioDeStock.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace GestioDeStock.Data.Repositories
{
    public class UnitRepository : BaseRepository<Unit>
    {
        public UnitRepository(DatabaseConnection database) : base(database, "units")
        {
        }

        public override async Task<int> AddAsync(Unit entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                INSERT INTO units (name, symbol, type, is_active, created_by, updated_by)
                VALUES (@Name, @Symbol, @Type, @IsActive, @CreatedBy, @UpdatedBy);
                SELECT LAST_INSERT_ID();";
            
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Unit entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE units SET 
                    name = @Name,
                    symbol = @Symbol,
                    type = @Type,
                    is_active = @IsActive,
                    updated_at = NOW(),
                    updated_by = @UpdatedBy
                WHERE id = @Id";
            
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public override async Task<IEnumerable<Unit>> SearchAsync(string searchTerm)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT * FROM units 
                WHERE name LIKE @SearchTerm 
                   OR symbol LIKE @SearchTerm
                ORDER BY name";
            
            return await connection.QueryAsync<Unit>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<IEnumerable<Unit>> GetActiveUnitsAsync()
        {
            return await GetActiveAsync();
        }

        public async Task<IEnumerable<Unit>> GetUnitsByTypeAsync(UnitType type)
        {
            using var connection = _database.CreateConnection();
            var sql = "SELECT * FROM units WHERE type = @Type AND is_active = 1 ORDER BY name";
            return await connection.QueryAsync<Unit>(sql, new { Type = type.ToString() });
        }

        public async Task<Unit?> GetBySymbolAsync(string symbol)
        {
            using var connection = _database.CreateConnection();
            var sql = "SELECT * FROM units WHERE symbol = @Symbol";
            return await connection.QueryFirstOrDefaultAsync<Unit>(sql, new { Symbol = symbol });
        }
    }
}
