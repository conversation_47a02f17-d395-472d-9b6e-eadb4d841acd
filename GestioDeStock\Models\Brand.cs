using System.Collections.Generic;

namespace GestioDeStock.Models
{
    public class Brand : BaseEntity
    {
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public byte[]? Logo { get; set; }
        public bool IsActive { get; set; } = true;

        // Navigation properties
        public List<Product> Products { get; set; } = new List<Product>();
    }
}
