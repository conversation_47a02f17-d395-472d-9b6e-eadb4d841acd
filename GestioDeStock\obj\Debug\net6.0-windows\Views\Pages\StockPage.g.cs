﻿#pragma checksum "..\..\..\..\..\Views\Pages\StockPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "064B535FCD5464E394F1987D181C87E6934E8F01"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GestioDeStock.Views.Pages {
    
    
    /// <summary>
    /// StockPage
    /// </summary>
    public partial class StockPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 24 "..\..\..\..\..\Views\Pages\StockPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StockAdjustmentButton;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\..\..\Views\Pages\StockPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InventoryButton;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\..\Views\Pages\StockPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\..\Views\Pages\StockPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox WarehouseComboBox;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\..\Views\Pages\StockPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StockStatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\..\Views\Pages\StockPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\..\Views\Pages\StockPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalProductsTextBlock;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\..\Views\Pages\StockPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StockValueTextBlock;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\..\Views\Pages\StockPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LowStockTextBlock;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\..\Views\Pages\StockPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OutOfStockTextBlock;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\..\..\Views\Pages\StockPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ResultsCountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\..\Views\Pages\StockPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\..\Views\Pages\StockPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintButton;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\..\Views\Pages\StockPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid StockDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GestioDeStock;V1.0.0.0;component/views/pages/stockpage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Pages\StockPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.StockAdjustmentButton = ((System.Windows.Controls.Button)(target));
            
            #line 28 "..\..\..\..\..\Views\Pages\StockPage.xaml"
            this.StockAdjustmentButton.Click += new System.Windows.RoutedEventHandler(this.StockAdjustmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.InventoryButton = ((System.Windows.Controls.Button)(target));
            
            #line 44 "..\..\..\..\..\Views\Pages\StockPage.xaml"
            this.InventoryButton.Click += new System.Windows.RoutedEventHandler(this.InventoryButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 80 "..\..\..\..\..\Views\Pages\StockPage.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.SearchTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.WarehouseComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 86 "..\..\..\..\..\Views\Pages\StockPage.xaml"
            this.WarehouseComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.WarehouseComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.StockStatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 92 "..\..\..\..\..\Views\Pages\StockPage.xaml"
            this.StockStatusComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.StockStatusComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 103 "..\..\..\..\..\Views\Pages\StockPage.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.TotalProductsTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.StockValueTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.LowStockTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.OutOfStockTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.ResultsCountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 158 "..\..\..\..\..\Views\Pages\StockPage.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.ExportButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.PrintButton = ((System.Windows.Controls.Button)(target));
            
            #line 173 "..\..\..\..\..\Views\Pages\StockPage.xaml"
            this.PrintButton.Click += new System.Windows.RoutedEventHandler(this.PrintButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.StockDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 193 "..\..\..\..\..\Views\Pages\StockPage.xaml"
            this.StockDataGrid.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.StockDataGrid_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 15:
            
            #line 239 "..\..\..\..\..\Views\Pages\StockPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AdjustStockButton_Click);
            
            #line default
            #line hidden
            break;
            case 16:
            
            #line 246 "..\..\..\..\..\Views\Pages\StockPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.HistoryButton_Click);
            
            #line default
            #line hidden
            break;
            case 17:
            
            #line 253 "..\..\..\..\..\Views\Pages\StockPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TransferButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

