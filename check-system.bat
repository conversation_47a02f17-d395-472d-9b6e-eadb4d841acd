@echo off
title Verification du Systeme - Gestion de Stock
color 0B

echo ========================================
echo   VERIFICATION DU SYSTEME
echo ========================================
echo.

echo Verification de .NET 6.0...
dotnet --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] .NET est installe
    dotnet --version
) else (
    echo [ERREUR] .NET 6.0 n'est pas installe
    echo Telechargez-le depuis: https://dotnet.microsoft.com/download/dotnet/6.0
)

echo.
echo Verification de MySQL...
mysql --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] MySQL est installe
    mysql --version
) else (
    echo [ERREUR] MySQL n'est pas installe ou pas dans le PATH
    echo Telechargez-le depuis: https://dev.mysql.com/downloads/mysql/
)

echo.
echo Verification de la base de donnees...
mysql -u root -p -e "USE gestion_stock; SELECT 'Base de donnees OK' as status;" 2>nul
if %ERRORLEVEL% EQU 0 (
    echo [OK] Base de donnees 'gestion_stock' accessible
) else (
    echo [ATTENTION] Base de donnees 'gestion_stock' non accessible
    echo Executez d'abord: mysql -u root -p ^< database.sql
)

echo.
echo Verification des dependances du projet...
cd GestioDeStock
dotnet restore --verbosity quiet >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] Dependances restaurees avec succes
) else (
    echo [ERREUR] Probleme avec les dependances
)

echo.
echo Verification de la compilation...
dotnet build --verbosity quiet >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo [OK] Projet compile avec succes
) else (
    echo [ERREUR] Erreurs de compilation
    echo Executez 'dotnet build' pour voir les details
)

cd ..

echo.
echo Verification des dossiers...
if exist "GestioDeStock" (
    echo [OK] Dossier du projet present
) else (
    echo [ERREUR] Dossier du projet manquant
)

if exist "database.sql" (
    echo [OK] Script de base de donnees present
) else (
    echo [ERREUR] Script de base de donnees manquant
)

echo.
echo ========================================
echo   RESUME DE LA VERIFICATION
echo ========================================
echo.
echo Si tous les elements sont [OK], vous pouvez
echo executer l'application avec 'start.bat'
echo.
echo En cas d'erreur, consultez INSTALLATION.md
echo pour les instructions detaillees.
echo.

pause
