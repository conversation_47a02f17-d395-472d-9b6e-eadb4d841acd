<Window x:Class="GestioDeStock.Views.Dialogs.ProductSearchDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Recherche de Produit" 
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Recherche de Produit" FontSize="20" FontWeight="Bold" Margin="0,0,0,20"/>
        
        <!-- Search -->
        <Grid Grid.Row="1" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBox x:Name="SearchTextBox" Grid.Column="0"
                   materialDesign:HintAssist.Hint="Rechercher par nom, code, code-barres..."
                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                   Margin="0,0,10,0"
                   TextChanged="SearchTextBox_TextChanged"/>
            
            <ComboBox x:Name="CategoryComboBox" Grid.Column="1"
                    materialDesign:HintAssist.Hint="Catégorie"
                    Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                    Margin="0,0,10,0"
                    SelectionChanged="CategoryComboBox_SelectionChanged"/>
            
            <Button x:Name="ClearButton" Grid.Column="2"
                  Style="{StaticResource MaterialDesignIconButton}"
                  ToolTip="Effacer"
                  Click="ClearButton_Click">
                <materialDesign:PackIcon Kind="Close"/>
            </Button>
        </Grid>
        
        <!-- Products List -->
        <DataGrid x:Name="ProductsDataGrid" Grid.Row="2"
                AutoGenerateColumns="False"
                CanUserAddRows="False"
                CanUserDeleteRows="False"
                IsReadOnly="True"
                SelectionMode="Single"
                MouseDoubleClick="ProductsDataGrid_MouseDoubleClick">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="Code" Binding="{Binding Code}" Width="100"/>
                <DataGridTextColumn Header="Nom" Binding="{Binding Name}" Width="200"/>
                <DataGridTextColumn Header="Catégorie" Binding="{Binding CategoryName}" Width="120"/>
                <DataGridTextColumn Header="Prix HT" Binding="{Binding PrixVenteHt, StringFormat='{}{0:N2} DA'}" Width="100"/>
                <DataGridTextColumn Header="Prix TTC" Binding="{Binding PrixVenteTtc, StringFormat='{}{0:N2} DA'}" Width="100"/>
                <DataGridTextColumn Header="Stock" Binding="{Binding StockQuantity, StringFormat='{}{0:N2}'}" Width="80"/>
                
                <DataGridTemplateColumn Header="Statut" Width="80">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Border CornerRadius="10" Padding="5,2" HorizontalAlignment="Center">
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding IsActive}" Value="True">
                                                <Setter Property="Background" Value="#4CAF50"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding IsActive}" Value="False">
                                                <Setter Property="Background" Value="#F44336"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Border.Style>
                                <TextBlock Text="{Binding IsActive, Converter={StaticResource BoolToStatusConverter}}" 
                                         Foreground="White" FontSize="10" FontWeight="Bold"/>
                            </Border>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                
                <DataGridTemplateColumn Header="Action" Width="80">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Button Style="{StaticResource MaterialDesignRaisedButton}" 
                                  Content="SÉLECTIONNER"
                                  Click="SelectButton_Click"
                                  Tag="{Binding}"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
        
        <!-- Buttons -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,15,0,0">
            <Button x:Name="CancelButton" 
                  Style="{StaticResource MaterialDesignOutlinedButton}"
                  Content="ANNULER" 
                  Margin="0,0,10,0"
                  Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
