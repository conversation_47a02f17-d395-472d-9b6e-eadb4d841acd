﻿<Window x:Class="GestioDeStock.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Gestion de Stock - Épicerie"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Top Bar -->
            <materialDesign:ColorZone Grid.Row="0" Mode="PrimaryMid" Padding="16">
                <DockPanel>
                    <StackPanel Orientation="Horizontal" DockPanel.Dock="Left">
                        <materialDesign:PackIcon Kind="Store" Width="32" Height="32"
                                               VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBlock Text="Gestion de Stock" FontSize="20" FontWeight="Medium"
                                 VerticalAlignment="Center"/>
                    </StackPanel>

                    <StackPanel Orientation="Horizontal" DockPanel.Dock="Right">
                        <TextBlock x:Name="UserNameTextBlock"
                                 VerticalAlignment="Center" Margin="0,0,15,0"/>
                        <Button x:Name="LogoutButton"
                              Style="{StaticResource MaterialDesignToolButton}"
                              ToolTip="Déconnexion"
                              Click="LogoutButton_Click">
                            <materialDesign:PackIcon Kind="Logout"/>
                        </Button>
                    </StackPanel>
                </DockPanel>
            </materialDesign:ColorZone>

            <!-- Main Content -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="250"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Side Menu -->
                <materialDesign:ColorZone Grid.Column="0" Mode="PrimaryDark">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="0,10">

                            <!-- Dashboard -->
                            <Button x:Name="DashboardButton" Style="{StaticResource MenuButtonStyle}"
                                  Click="DashboardButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ViewDashboard" Width="20" Height="20"
                                                           VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBlock Text="Tableau de Bord" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- Sales -->
                            <Button x:Name="SalesButton" Style="{StaticResource MenuButtonStyle}"
                                  Click="SalesButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="CashRegister" Width="20" Height="20"
                                                           VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBlock Text="Point de Vente" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- Products -->
                            <Button x:Name="ProductsButton" Style="{StaticResource MenuButtonStyle}"
                                  Click="ProductsButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Package" Width="20" Height="20"
                                                           VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBlock Text="Produits" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- Purchases -->
                            <Button x:Name="PurchasesButton" Style="{StaticResource MenuButtonStyle}"
                                  Click="PurchasesButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ShoppingCart" Width="20" Height="20"
                                                           VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBlock Text="Achats" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- Stock -->
                            <Button x:Name="StockButton" Style="{StaticResource MenuButtonStyle}"
                                  Click="StockButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Warehouse" Width="20" Height="20"
                                                           VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBlock Text="Stock" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- Clients -->
                            <Button x:Name="ClientsButton" Style="{StaticResource MenuButtonStyle}"
                                  Click="ClientsButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="AccountGroup" Width="20" Height="20"
                                                           VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBlock Text="Clients" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- Suppliers -->
                            <Button x:Name="SuppliersButton" Style="{StaticResource MenuButtonStyle}"
                                  Click="SuppliersButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="TruckDelivery" Width="20" Height="20"
                                                           VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBlock Text="Fournisseurs" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- Cash -->
                            <Button x:Name="CashButton" Style="{StaticResource MenuButtonStyle}"
                                  Click="CashButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Cash" Width="20" Height="20"
                                                           VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBlock Text="Caisse" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- Reports -->
                            <Button x:Name="ReportsButton" Style="{StaticResource MenuButtonStyle}"
                                  Click="ReportsButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ChartLine" Width="20" Height="20"
                                                           VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBlock Text="Rapports" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- Settings -->
                            <Button x:Name="SettingsButton" Style="{StaticResource MenuButtonStyle}"
                                  Click="SettingsButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Settings" Width="20" Height="20"
                                                           VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    <TextBlock Text="Paramètres" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                        </StackPanel>
                    </ScrollViewer>
                </materialDesign:ColorZone>

                <!-- Content Area -->
                <Frame x:Name="MainFrame" Grid.Column="1" NavigationUIVisibility="Hidden"/>

            </Grid>

            <!-- Status Bar -->
            <materialDesign:ColorZone Grid.Row="2" Mode="PrimaryLight" Padding="10,5">
                <DockPanel>
                    <TextBlock x:Name="StatusTextBlock" Text="Prêt" DockPanel.Dock="Left"/>
                    <TextBlock x:Name="DateTimeTextBlock" DockPanel.Dock="Right" HorizontalAlignment="Right"/>
                </DockPanel>
            </materialDesign:ColorZone>

        </Grid>
    </materialDesign:DialogHost>
</Window>
