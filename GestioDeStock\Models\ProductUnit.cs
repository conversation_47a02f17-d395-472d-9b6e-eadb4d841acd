namespace GestioDeStock.Models
{
    public class ProductUnit : BaseEntity
    {
        public int ProductId { get; set; }
        public int UnitId { get; set; }
        public string UnitName { get; set; } = string.Empty;
        public decimal ConversionFactor { get; set; } = 1.000m;
        public decimal PrixVenteHt { get; set; } = 0.00m;
        public decimal PrixVenteTtc { get; set; } = 0.00m;
        public string? Barcode { get; set; }
        public bool IsDefault { get; set; } = false;

        // Navigation properties
        public Product Product { get; set; } = null!;
        public Unit Unit { get; set; } = null!;
    }
}
