namespace GestioDeStock.Models
{
    public class PromotionProduct : BaseEntity
    {
        public int PromotionId { get; set; }
        public int ProductId { get; set; }
        public decimal Quantity { get; set; } = 1.000m;
        public decimal? SpecialPrice { get; set; }

        // Navigation properties
        public Promotion Promotion { get; set; } = null!;
        public Product Product { get; set; } = null!;
    }
}
