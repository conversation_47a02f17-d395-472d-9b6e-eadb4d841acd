using GestioDeStock.Data;
using GestioDeStock.Data.Repositories;
using GestioDeStock.Models;
using GestioDeStock.Services;
using System.Windows;

namespace GestioDeStock.Views.Dialogs
{
    public partial class ProductDialog : Window
    {
        private readonly AuthenticationService _authService;
        private readonly ProductRepository _productRepository;
        private readonly CategoryRepository _categoryRepository;
        private readonly BrandRepository _brandRepository;
        private readonly UnitRepository _unitRepository;
        private Product? _product;
        private bool _isEditMode;

        public ProductDialog(AuthenticationService authService, Product? product = null)
        {
            InitializeComponent();
            _authService = authService;
            _product = product;
            _isEditMode = product != null;

            var config = ConfigurationService.Instance;
            var database = new DatabaseConnection(config.GetConnectionString());
            _productRepository = new ProductRepository(database);
            _categoryRepository = new CategoryRepository(database);
            _brandRepository = new BrandRepository(database);
            _unitRepository = new UnitRepository(database);

            InitializeDialog();
        }

        private async void InitializeDialog()
        {
            try
            {
                // Set header
                HeaderTextBlock.Text = _isEditMode ? "Modifier le Produit" : "Nouveau Produit";

                // Load combo box data
                await LoadComboBoxData();

                // Load product data if editing
                if (_isEditMode && _product != null)
                {
                    LoadProductData();
                }

                // Set default TVA rate
                if (!_isEditMode)
                {
                    var config = ConfigurationService.Instance;
                    TVATextBox.Text = config.DefaultTVARate.ToString("F2");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'initialisation: {ex.Message}", 
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadComboBoxData()
        {
            // Load categories
            var categories = await _categoryRepository.GetActiveCategoriesAsync();
            CategoryComboBox.ItemsSource = categories;
            CategoryComboBox.DisplayMemberPath = "Name";
            CategoryComboBox.SelectedValuePath = "Id";

            // Load brands
            var brands = await _brandRepository.GetActiveBrandsAsync();
            BrandComboBox.ItemsSource = brands;
            BrandComboBox.DisplayMemberPath = "Name";
            BrandComboBox.SelectedValuePath = "Id";

            // Load units
            var units = await _unitRepository.GetActiveUnitsAsync();
            UnitComboBox.ItemsSource = units;
            UnitComboBox.DisplayMemberPath = "Name";
            UnitComboBox.SelectedValuePath = "Id";
        }

        private void LoadProductData()
        {
            if (_product == null) return;

            CodeTextBox.Text = _product.Code;
            NameTextBox.Text = _product.Name;
            DescriptionTextBox.Text = _product.Description;
            CategoryComboBox.SelectedValue = _product.CategoryId;
            BrandComboBox.SelectedValue = _product.BrandId;
            UnitComboBox.SelectedValue = _product.UnitId;
            ExpiryDatePicker.SelectedDate = _product.ExpiryDate;
            
            PurchasePriceTextBox.Text = _product.PrixAchat.ToString("F2");
            SalePriceHTTextBox.Text = _product.PrixVenteHt.ToString("F2");
            SalePriceTTCTextBox.Text = _product.PrixVenteTtc.ToString("F2");
            TVATextBox.Text = _product.TvaRate.ToString("F2");
            PMPTextBox.Text = _product.Pmp.ToString("F2");
            
            PiecesPerFardeauTextBox.Text = _product.PiecesPerFardeau?.ToString() ?? "1";
            FardeauPriceTextBox.Text = _product.PrixFardeau?.ToString("F2") ?? "";
            MinStockTextBox.Text = _product.MinStockAlert.ToString("F2");
            MaxStockTextBox.Text = _product.MaxStockAlert?.ToString("F2") ?? "";
            
            IsScaleProductCheckBox.IsChecked = _product.IsScaleProduct;
            ScalePrefixTextBox.Text = _product.ScalePrefix;
            IsActiveCheckBox.IsChecked = _product.IsActive;
            IsArchivedCheckBox.IsChecked = _product.IsArchived;

            // Load primary barcode
            // TODO: Load from product_barcodes table
        }

        private void PriceTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            CalculatePrices();
        }

        private void CalculatePrices()
        {
            try
            {
                if (decimal.TryParse(SalePriceHTTextBox.Text, out decimal priceHT) &&
                    decimal.TryParse(TVATextBox.Text, out decimal tvaRate))
                {
                    var priceTTC = priceHT * (1 + tvaRate / 100);
                    SalePriceTTCTextBox.Text = priceTTC.ToString("F2");
                }
                else if (decimal.TryParse(SalePriceTTCTextBox.Text, out decimal priceTTC) &&
                         decimal.TryParse(TVATextBox.Text, out decimal tvaRate2))
                {
                    var priceHT2 = priceTTC / (1 + tvaRate2 / 100);
                    SalePriceHTTextBox.Text = priceHT2.ToString("F2");
                }
            }
            catch
            {
                // Ignore calculation errors
            }
        }

        private void IsScaleProductCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            ScalePrefixTextBox.IsEnabled = true;
            if (string.IsNullOrEmpty(ScalePrefixTextBox.Text))
            {
                ScalePrefixTextBox.Text = "22"; // Default weight prefix
            }
        }

        private void IsScaleProductCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            ScalePrefixTextBox.IsEnabled = false;
            ScalePrefixTextBox.Text = "";
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var product = _isEditMode ? _product! : new Product();

                // Basic information
                product.Code = CodeTextBox.Text.Trim();
                product.Name = NameTextBox.Text.Trim();
                product.Description = DescriptionTextBox.Text.Trim();
                product.CategoryId = CategoryComboBox.SelectedValue as int?;
                product.BrandId = BrandComboBox.SelectedValue as int?;
                product.UnitId = (int)UnitComboBox.SelectedValue;
                product.ExpiryDate = ExpiryDatePicker.SelectedDate;

                // Pricing
                product.PrixAchat = decimal.Parse(PurchasePriceTextBox.Text);
                product.PrixVenteHt = decimal.Parse(SalePriceHTTextBox.Text);
                product.PrixVenteTtc = decimal.Parse(SalePriceTTCTextBox.Text);
                product.TvaRate = decimal.Parse(TVATextBox.Text);
                
                if (int.TryParse(PiecesPerFardeauTextBox.Text, out int pieces))
                    product.PiecesPerFardeau = pieces;
                
                if (decimal.TryParse(FardeauPriceTextBox.Text, out decimal fardeauPrice))
                    product.PrixFardeau = fardeauPrice;

                product.MinStockAlert = decimal.Parse(MinStockTextBox.Text);
                
                if (decimal.TryParse(MaxStockTextBox.Text, out decimal maxStock))
                    product.MaxStockAlert = maxStock;

                // Options
                product.IsScaleProduct = IsScaleProductCheckBox.IsChecked == true;
                product.ScalePrefix = IsScaleProductCheckBox.IsChecked == true ? ScalePrefixTextBox.Text : null;
                product.IsActive = IsActiveCheckBox.IsChecked == true;
                product.IsArchived = IsArchivedCheckBox.IsChecked == true;

                // Audit fields
                if (_isEditMode)
                {
                    product.UpdatedBy = _authService.CurrentUser?.Id;
                }
                else
                {
                    product.CreatedBy = _authService.CurrentUser?.Id;
                    product.UpdatedBy = _authService.CurrentUser?.Id;
                }

                // Save to database
                if (_isEditMode)
                {
                    await _productRepository.UpdateAsync(product);
                }
                else
                {
                    await _productRepository.AddAsync(product);
                }

                // TODO: Save barcode if provided

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'enregistrement: {ex.Message}", 
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(CodeTextBox.Text))
            {
                MessageBox.Show("Le code produit est obligatoire.", "Validation", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                CodeTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                MessageBox.Show("Le nom du produit est obligatoire.", "Validation", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return false;
            }

            if (UnitComboBox.SelectedValue == null)
            {
                MessageBox.Show("L'unité de base est obligatoire.", "Validation", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                UnitComboBox.Focus();
                return false;
            }

            if (!decimal.TryParse(PurchasePriceTextBox.Text, out _))
            {
                MessageBox.Show("Le prix d'achat doit être un nombre valide.", "Validation", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                PurchasePriceTextBox.Focus();
                return false;
            }

            if (!decimal.TryParse(SalePriceHTTextBox.Text, out _))
            {
                MessageBox.Show("Le prix de vente HT doit être un nombre valide.", "Validation", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                SalePriceHTTextBox.Focus();
                return false;
            }

            if (!decimal.TryParse(SalePriceTTCTextBox.Text, out _))
            {
                MessageBox.Show("Le prix de vente TTC doit être un nombre valide.", "Validation", 
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                SalePriceTTCTextBox.Focus();
                return false;
            }

            return true;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
