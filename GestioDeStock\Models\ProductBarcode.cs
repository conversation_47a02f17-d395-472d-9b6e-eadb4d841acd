namespace GestioDeStock.Models
{
    public class ProductBarcode : BaseEntity
    {
        public int ProductId { get; set; }
        public string Barcode { get; set; } = string.Empty;
        public bool IsPrimary { get; set; } = false;
        public bool IsScaleBarcode { get; set; } = false;
        public decimal UnitQuantity { get; set; } = 1.000m;
        public decimal? UnitPrice { get; set; }

        // Navigation properties
        public Product Product { get; set; } = null!;
    }
}
