using System;

namespace GestioDeStock.Models
{
    public class Expense : BaseEntity
    {
        public string ReferenceNumber { get; set; } = string.Empty;
        public int CategoryId { get; set; }
        public ExpenseType Type { get; set; }
        public string Description { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public PaymentMethodType PaymentMethod { get; set; } = PaymentMethodType.Espèce;
        public int? BankAccountId { get; set; }
        public string? CheckNumber { get; set; }
        public DateTime? CheckDate { get; set; }
        public DateTime TransactionDate { get; set; } = DateTime.Now;
        public byte[]? ReceiptImage { get; set; }
        public string? Notes { get; set; }

        // Navigation properties
        public ExpenseCategory Category { get; set; } = null!;
        public BankAccount? BankAccount { get; set; }
    }
}
