﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <ApplicationIcon>icon.ico</ApplicationIcon>
    <AssemblyTitle>Gestion de Stock</AssemblyTitle>
    <AssemblyDescription>Système de Gestion de Stock pour Épicerie</AssemblyDescription>
    <AssemblyCompany>Mon Épicerie</AssemblyCompany>
    <AssemblyProduct>Gestion de Stock</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2024</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="MySql.Data" Version="8.2.0" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.0" />
    <PackageReference Include="ZXing.Net" Version="0.16.9" />
    <PackageReference Include="iTextSharp" Version="5.5.13.3" />
  </ItemGroup>

</Project>
