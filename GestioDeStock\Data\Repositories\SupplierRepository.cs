using Dapper;
using GestioDeStock.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace GestioDeStock.Data.Repositories
{
    public class SupplierRepository : BaseRepository<Supplier>
    {
        public SupplierRepository(DatabaseConnection database) : base(database, "suppliers")
        {
        }

        public override async Task<int> AddAsync(Supplier entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                INSERT INTO suppliers (code, name, address, phone, email, nif, nis, rc, article, 
                    credit_limit, opening_balance, current_balance, is_active, is_archived, 
                    created_by, updated_by)
                VALUES (@Code, @Name, @Address, @Phone, @Email, @Nif, @Nis, @Rc, @Article,
                    @CreditLimit, @OpeningBalance, @CurrentBalance, @IsActive, @IsArchived,
                    @CreatedBy, @UpdatedBy);
                SELECT LAST_INSERT_ID();";
            
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Supplier entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE suppliers SET 
                    code = @Code,
                    name = @Name,
                    address = @Address,
                    phone = @Phone,
                    email = @Email,
                    nif = @Nif,
                    nis = @Nis,
                    rc = @Rc,
                    article = @Article,
                    credit_limit = @CreditLimit,
                    opening_balance = @OpeningBalance,
                    current_balance = @CurrentBalance,
                    is_active = @IsActive,
                    is_archived = @IsArchived,
                    updated_at = NOW(),
                    updated_by = @UpdatedBy
                WHERE id = @Id";
            
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public override async Task<IEnumerable<Supplier>> SearchAsync(string searchTerm)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT * FROM suppliers 
                WHERE name LIKE @SearchTerm 
                   OR code LIKE @SearchTerm 
                   OR phone LIKE @SearchTerm
                   OR email LIKE @SearchTerm
                ORDER BY name";
            
            return await connection.QueryAsync<Supplier>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<IEnumerable<Supplier>> GetActiveSuppliersAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = "SELECT * FROM suppliers WHERE is_active = 1 AND is_archived = 0 ORDER BY name";
            return await connection.QueryAsync<Supplier>(sql);
        }

        public async Task<Supplier?> GetByCodeAsync(string code)
        {
            using var connection = _database.CreateConnection();
            var sql = "SELECT * FROM suppliers WHERE code = @Code";
            return await connection.QueryFirstOrDefaultAsync<Supplier>(sql, new { Code = code });
        }

        public async Task<bool> UpdateBalanceAsync(int supplierId, decimal amount)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE suppliers 
                SET current_balance = current_balance + @Amount,
                    updated_at = NOW()
                WHERE id = @SupplierId";
            
            var affectedRows = await connection.ExecuteAsync(sql, new { SupplierId = supplierId, Amount = amount });
            return affectedRows > 0;
        }

        public async Task<string> GetNextSupplierCodeAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT CONCAT('SP', LPAD(COALESCE(MAX(CAST(SUBSTRING(code, 3) AS UNSIGNED)), 0) + 1, 6, '0'))
                FROM suppliers 
                WHERE code LIKE 'SP%'";
            
            return await connection.QuerySingleAsync<string>(sql);
        }
    }
}
