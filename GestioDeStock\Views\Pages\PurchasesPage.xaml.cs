using GestioDeStock.Data;
using GestioDeStock.Data.Repositories;
using GestioDeStock.Models;
using GestioDeStock.Services;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace GestioDeStock.Views.Pages
{
    public partial class PurchasesPage : Page
    {
        private readonly AuthenticationService _authService;
        private readonly PurchaseInvoiceRepository _purchaseRepository;
        private readonly SupplierRepository _supplierRepository;
        private ObservableCollection<PurchaseInvoice> _purchases;
        private List<PurchaseInvoice> _allPurchases;

        public PurchasesPage(AuthenticationService authService)
        {
            InitializeComponent();
            _authService = authService;

            var config = ConfigurationService.Instance;
            var database = new DatabaseConnection(config.GetConnectionString());
            _purchaseRepository = new PurchaseInvoiceRepository(database);
            _supplierRepository = new SupplierRepository(database);

            _purchases = new ObservableCollection<PurchaseInvoice>();
            _allPurchases = new List<PurchaseInvoice>();

            PurchasesDataGrid.ItemsSource = _purchases;

            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                // Load suppliers for filter
                var suppliers = await _supplierRepository.GetActiveSuppliersAsync();
                SupplierComboBox.ItemsSource = suppliers;
                SupplierComboBox.DisplayMemberPath = "Name";
                SupplierComboBox.SelectedValuePath = "Id";

                // Set default status filter
                StatusComboBox.SelectedIndex = 0; // All

                // Load purchases
                await LoadPurchases();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des données: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadPurchases()
        {
            try
            {
                _allPurchases = (await _purchaseRepository.GetAllAsync()).ToList();
                ApplyFilters();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des achats: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplyFilters()
        {
            var filteredPurchases = _allPurchases.AsEnumerable();

            // Search filter
            if (!string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                var searchTerm = SearchTextBox.Text.ToLower();
                filteredPurchases = filteredPurchases.Where(p =>
                    p.InvoiceNumber.ToLower().Contains(searchTerm) ||
                    (p.SupplierInvoiceNumber?.ToLower().Contains(searchTerm) ?? false));
            }

            // Supplier filter
            if (SupplierComboBox.SelectedValue != null)
            {
                var supplierId = (int)SupplierComboBox.SelectedValue;
                filteredPurchases = filteredPurchases.Where(p => p.SupplierId == supplierId);
            }

            // Status filter
            if (StatusComboBox.SelectedIndex > 0)
            {
                var statusText = ((ComboBoxItem)StatusComboBox.SelectedItem).Content.ToString();
                filteredPurchases = filteredPurchases.Where(p => p.Status.ToString() == statusText);
            }

            var results = filteredPurchases.ToList();

            // Update UI
            _purchases.Clear();
            foreach (var purchase in results)
            {
                _purchases.Add(purchase);
            }

            UpdateSummary(results);
            ResultsCountTextBlock.Text = $"{results.Count} achats trouvés";
        }

        private void UpdateSummary(List<PurchaseInvoice> purchases)
        {
            var totalHT = purchases.Sum(p => p.SubtotalHt);
            var totalTVA = purchases.Sum(p => p.TotalTva);
            var totalTTC = purchases.Sum(p => p.TotalTtc);

            TotalHTTextBlock.Text = $"{totalHT:N2} DA";
            TotalTVATextBlock.Text = $"{totalTVA:N2} DA";
            TotalTTCTextBlock.Text = $"{totalTTC:N2} DA";
            CountTextBlock.Text = purchases.Count.ToString();
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void SupplierComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void StatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadPurchases();
        }

        private void NewPurchaseButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Open new purchase dialog
            MessageBox.Show("Nouvelle facture d'achat à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ViewButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is PurchaseInvoice purchase)
            {
                // TODO: Open purchase details dialog
                MessageBox.Show($"Voir les détails de la facture {purchase.InvoiceNumber}", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is PurchaseInvoice purchase)
            {
                // TODO: Print purchase invoice
                MessageBox.Show($"Imprimer la facture {purchase.InvoiceNumber}", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is PurchaseInvoice purchase)
            {
                var result = MessageBox.Show(
                    $"Êtes-vous sûr de vouloir supprimer la facture '{purchase.InvoiceNumber}' ?",
                    "Confirmation de suppression",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await _purchaseRepository.DeleteAsync(purchase.Id);
                        await LoadPurchases();
                        MessageBox.Show("Facture supprimée avec succès.", "Succès",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Erreur lors de la suppression: {ex.Message}",
                                      "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void PurchasesDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (PurchasesDataGrid.SelectedItem is PurchaseInvoice purchase)
            {
                // TODO: Open purchase details dialog
                MessageBox.Show($"Voir les détails de la facture {purchase.InvoiceNumber}", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement export functionality
            MessageBox.Show("Fonctionnalité d'export à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
