using GestioDeStock.Data;
using GestioDeStock.Data.Repositories;
using GestioDeStock.Models;
using GestioDeStock.Services;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace GestioDeStock.Views.Pages
{
    public partial class ClientsPage : Page
    {
        private readonly AuthenticationService _authService;
        private readonly ClientRepository _clientRepository;
        private ObservableCollection<Client> _clients;
        private List<Client> _allClients;

        public ClientsPage(AuthenticationService authService)
        {
            InitializeComponent();
            _authService = authService;

            var config = ConfigurationService.Instance;
            var database = new DatabaseConnection(config.GetConnectionString());
            _clientRepository = new ClientRepository(database);

            _clients = new ObservableCollection<Client>();
            _allClients = new List<Client>();

            ClientsDataGrid.ItemsSource = _clients;

            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                // Set default status filter
                StatusComboBox.SelectedIndex = 0; // All

                // Load clients
                await LoadClients();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des données: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadClients()
        {
            try
            {
                _allClients = (await _clientRepository.GetAllAsync()).ToList();
                ApplyFilters();
                UpdateSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des clients: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ApplyFilters()
        {
            var filteredClients = _allClients.AsEnumerable();

            // Search filter
            if (!string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                var searchTerm = SearchTextBox.Text.ToLower();
                filteredClients = filteredClients.Where(c =>
                    c.Name.ToLower().Contains(searchTerm) ||
                    (c.Phone?.ToLower().Contains(searchTerm) ?? false) ||
                    (c.Email?.ToLower().Contains(searchTerm) ?? false) ||
                    c.Code.ToLower().Contains(searchTerm));
            }

            // Status filter
            if (StatusComboBox.SelectedIndex > 0)
            {
                var statusText = ((ComboBoxItem)StatusComboBox.SelectedItem).Content.ToString();
                switch (statusText)
                {
                    case "Actifs":
                        filteredClients = filteredClients.Where(c => c.IsActive);
                        break;
                    case "Inactifs":
                        filteredClients = filteredClients.Where(c => !c.IsActive);
                        break;
                    case "Avec Crédit":
                        filteredClients = filteredClients.Where(c => c.CurrentBalance > 0);
                        break;
                }
            }

            var results = filteredClients.ToList();

            // Update UI
            _clients.Clear();
            foreach (var client in results)
            {
                _clients.Add(client);
            }

            ResultsCountTextBlock.Text = $"{results.Count} clients trouvés";
        }

        private void UpdateSummary()
        {
            var totalClients = _allClients.Count;
            var activeClients = _allClients.Count(c => c.IsActive);
            var totalDebt = _allClients.Sum(c => c.CurrentBalance);
            var totalCreditLimit = _allClients.Sum(c => c.CreditLimit);

            TotalClientsTextBlock.Text = totalClients.ToString();
            ActiveClientsTextBlock.Text = activeClients.ToString();
            TotalDebtTextBlock.Text = $"{totalDebt:N2} DA";
            TotalCreditLimitTextBlock.Text = $"{totalCreditLimit:N2} DA";
        }

        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            ApplyFilters();
        }

        private void StatusComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            ApplyFilters();
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadClients();
        }

        private async void NewClientButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new Views.Dialogs.AddClientDialog(_authService);
            if (dialog.ShowDialog() == true && dialog.NewClient != null)
            {
                await LoadClients();
                MessageBox.Show($"Client '{dialog.NewClient.Name}' ajouté avec succès!", "Succès",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Client client)
            {
                // TODO: Open edit client dialog
                MessageBox.Show($"Modifier le client {client.Name}", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void AccountButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Client client)
            {
                // TODO: Open client account dialog
                MessageBox.Show($"Compte du client {client.Name}", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void HistoryButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Client client)
            {
                // TODO: Open client history dialog
                MessageBox.Show($"Historique du client {client.Name}", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Client client)
            {
                var result = MessageBox.Show(
                    $"Êtes-vous sûr de vouloir supprimer le client '{client.Name}' ?",
                    "Confirmation de suppression",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        await _clientRepository.DeleteAsync(client.Id);
                        await LoadClients();
                        MessageBox.Show("Client supprimé avec succès.", "Succès",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Erreur lors de la suppression: {ex.Message}",
                                      "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void ClientsDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (ClientsDataGrid.SelectedItem is Client client)
            {
                // TODO: Open client details dialog
                MessageBox.Show($"Détails du client {client.Name}", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement export functionality
            MessageBox.Show("Fonctionnalité d'export à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
