using Dapper;
using GestioDeStock.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace GestioDeStock.Data.Repositories
{
    public class ClientRepository : BaseRepository<Client>
    {
        public ClientRepository(DatabaseConnection database) : base(database, "clients")
        {
        }

        public override async Task<int> AddAsync(Client entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                INSERT INTO clients (code, name, address, phone, email, nif, nis, rc, article, 
                    credit_limit, opening_balance, current_balance, is_active, is_archived, 
                    created_by, updated_by)
                VALUES (@Code, @Name, @Address, @Phone, @Email, @Nif, @Nis, @Rc, @Article,
                    @CreditLimit, @OpeningBalance, @CurrentBalance, @IsActive, @IsArchived,
                    @CreatedBy, @UpdatedBy);
                SELECT LAST_INSERT_ID();";
            
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Client entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE clients SET 
                    code = @Code,
                    name = @Name,
                    address = @Address,
                    phone = @Phone,
                    email = @Email,
                    nif = @Nif,
                    nis = @Nis,
                    rc = @Rc,
                    article = @Article,
                    credit_limit = @CreditLimit,
                    opening_balance = @OpeningBalance,
                    current_balance = @CurrentBalance,
                    is_active = @IsActive,
                    is_archived = @IsArchived,
                    updated_at = NOW(),
                    updated_by = @UpdatedBy
                WHERE id = @Id";
            
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public override async Task<IEnumerable<Client>> SearchAsync(string searchTerm)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT * FROM clients 
                WHERE name LIKE @SearchTerm 
                   OR code LIKE @SearchTerm 
                   OR phone LIKE @SearchTerm
                   OR email LIKE @SearchTerm
                ORDER BY name";
            
            return await connection.QueryAsync<Client>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<IEnumerable<Client>> GetActiveClientsAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = "SELECT * FROM clients WHERE is_active = 1 AND is_archived = 0 ORDER BY name";
            return await connection.QueryAsync<Client>(sql);
        }

        public async Task<Client?> GetByCodeAsync(string code)
        {
            using var connection = _database.CreateConnection();
            var sql = "SELECT * FROM clients WHERE code = @Code";
            return await connection.QueryFirstOrDefaultAsync<Client>(sql, new { Code = code });
        }

        public async Task<Client?> GetByPhoneAsync(string phone)
        {
            using var connection = _database.CreateConnection();
            var sql = "SELECT * FROM clients WHERE phone = @Phone";
            return await connection.QueryFirstOrDefaultAsync<Client>(sql, new { Phone = phone });
        }

        public async Task<bool> UpdateBalanceAsync(int clientId, decimal amount)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE clients 
                SET current_balance = current_balance + @Amount,
                    updated_at = NOW()
                WHERE id = @ClientId";
            
            var affectedRows = await connection.ExecuteAsync(sql, new { ClientId = clientId, Amount = amount });
            return affectedRows > 0;
        }

        public async Task<decimal> GetClientBalanceAsync(int clientId)
        {
            using var connection = _database.CreateConnection();
            var sql = "SELECT current_balance FROM clients WHERE id = @ClientId";
            return await connection.QuerySingleOrDefaultAsync<decimal>(sql, new { ClientId = clientId });
        }

        public async Task<IEnumerable<Client>> GetClientsWithDebtAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT * FROM clients 
                WHERE current_balance > 0 
                  AND is_active = 1 
                  AND is_archived = 0 
                ORDER BY current_balance DESC";
            
            return await connection.QueryAsync<Client>(sql);
        }

        public async Task<string> GetNextClientCodeAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT CONCAT('CL', LPAD(COALESCE(MAX(CAST(SUBSTRING(code, 3) AS UNSIGNED)), 0) + 1, 6, '0'))
                FROM clients 
                WHERE code LIKE 'CL%'";
            
            return await connection.QuerySingleAsync<string>(sql);
        }
    }
}
