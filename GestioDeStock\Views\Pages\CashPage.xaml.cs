using GestioDeStock.Data;
using GestioDeStock.Data.Repositories;
using GestioDeStock.Models;
using GestioDeStock.Services;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace GestioDeStock.Views.Pages
{
    public partial class CashPage : Page
    {
        private readonly AuthenticationService _authService;
        private readonly CashSessionRepository _sessionRepository;
        private readonly ExpenseRepository _expenseRepository;
        private ObservableCollection<CashSession> _sessions;
        private CashSession? _currentSession;

        public CashPage(AuthenticationService authService)
        {
            InitializeComponent();
            _authService = authService;

            var config = ConfigurationService.Instance;
            var database = new DatabaseConnection(config.GetConnectionString());
            _sessionRepository = new CashSessionRepository(database);
            _expenseRepository = new ExpenseRepository(database);

            _sessions = new ObservableCollection<CashSession>();
            SessionsDataGrid.ItemsSource = _sessions;

            // Set default date range
            FromDatePicker.SelectedDate = DateTime.Today.AddDays(-30);
            ToDatePicker.SelectedDate = DateTime.Today;

            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                await LoadCurrentSession();
                await LoadSessions();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des données: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadCurrentSession()
        {
            try
            {
                var userId = _authService.CurrentUser?.Id ?? 0;
                var warehouseId = 1; // TODO: Get from settings

                _currentSession = await _sessionRepository.GetActiveSessionAsync(userId, warehouseId);

                if (_currentSession != null)
                {
                    SessionNumberTextBlock.Text = _currentSession.SessionNumber;
                    OpeningTimeTextBlock.Text = _currentSession.OpenedAt.ToString("HH:mm");
                    OpeningAmountTextBlock.Text = $"{_currentSession.OpeningAmount:N2} DA";
                    CurrentSalesTextBlock.Text = $"{_currentSession.TotalSales:N2} DA";
                    SessionStatusTextBlock.Text = "Ouverte";
                    SessionStatusTextBlock.Foreground = System.Windows.Media.Brushes.Green;

                    OpenCashButton.IsEnabled = false;
                    CloseCashButton.IsEnabled = true;
                }
                else
                {
                    SessionNumberTextBlock.Text = "Aucune";
                    OpeningTimeTextBlock.Text = "--:--";
                    OpeningAmountTextBlock.Text = "0.00 DA";
                    CurrentSalesTextBlock.Text = "0.00 DA";
                    SessionStatusTextBlock.Text = "Fermée";
                    SessionStatusTextBlock.Foreground = System.Windows.Media.Brushes.Red;

                    OpenCashButton.IsEnabled = true;
                    CloseCashButton.IsEnabled = false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement de la session actuelle: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadSessions()
        {
            try
            {
                var fromDate = FromDatePicker.SelectedDate ?? DateTime.Today.AddDays(-30);
                var toDate = ToDatePicker.SelectedDate ?? DateTime.Today;

                var sessions = await _sessionRepository.GetSessionsByDateAsync(fromDate, null);
                var filteredSessions = sessions.Where(s => s.OpenedAt.Date >= fromDate.Date && s.OpenedAt.Date <= toDate.Date);

                _sessions.Clear();
                foreach (var session in filteredSessions.OrderByDescending(s => s.OpenedAt))
                {
                    _sessions.Add(session);
                }

                UpdateSummary(filteredSessions.ToList());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des sessions: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateSummary(List<CashSession> sessions)
        {
            var totalSessions = sessions.Count;
            var totalCash = sessions.Sum(s => s.TotalCash);
            var totalCard = sessions.Sum(s => s.TotalCard);
            var totalSales = sessions.Sum(s => s.TotalSales);

            TotalSessionsTextBlock.Text = totalSessions.ToString();
            TotalCashTextBlock.Text = $"{totalCash:N2} DA";
            TotalCardTextBlock.Text = $"{totalCard:N2} DA";
            TotalSalesTextBlock.Text = $"{totalSales:N2} DA";
        }

        private async void OpenCashButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new Views.Dialogs.OpenCashDialog(_authService);
                if (dialog.ShowDialog() == true)
                {
                    var newSession = new CashSession
                    {
                        SessionNumber = dialog.SessionNumber,
                        UserId = _authService.CurrentUser?.Id ?? 0,
                        WarehouseId = 1, // TODO: Get from settings
                        OpeningAmount = dialog.OpeningAmount,
                        OpenedAt = DateTime.Now,
                        IsClosed = false,
                        Notes = dialog.Notes,
                        CreatedBy = _authService.CurrentUser?.Id ?? 0,
                        UpdatedBy = _authService.CurrentUser?.Id ?? 0
                    };

                    await _sessionRepository.AddAsync(newSession);
                    await LoadCurrentSession();
                    await LoadSessions();

                    MessageBox.Show("Session de caisse ouverte avec succès!", "Succès",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'ouverture de la caisse: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void CloseCashButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentSession == null) return;

            try
            {
                var dialog = new Views.Dialogs.CloseCashDialog(_currentSession);
                if (dialog.ShowDialog() == true)
                {
                    await _sessionRepository.CloseSessionAsync(_currentSession.Id, dialog.ClosingAmount, dialog.Notes);
                    await LoadCurrentSession();
                    await LoadSessions();

                    var message = "Session de caisse fermée avec succès!";
                    if (Math.Abs(dialog.Difference) > 0)
                    {
                        message += $"\nDifférence: {(dialog.Difference >= 0 ? "+" : "")}{dialog.Difference:N2} DA";
                    }

                    MessageBox.Show(message, "Succès",
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de la fermeture de la caisse: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddExpenseButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new Views.Dialogs.AddExpenseDialog(_authService);
            if (dialog.ShowDialog() == true && dialog.NewExpense != null)
            {
                MessageBox.Show($"Dépense '{dialog.NewExpense.Description}' ajoutée avec succès!", "Succès",
                              MessageBoxButton.OK, MessageBoxImage.Information);
                // Refresh data if needed
                LoadData();
            }
        }

        private void AddIncomeButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new Views.Dialogs.AddExpenseDialog(_authService);
            if (dialog.ShowDialog() == true && dialog.NewExpense != null)
            {
                MessageBox.Show($"Recette '{dialog.NewExpense.Description}' ajoutée avec succès!", "Succès",
                              MessageBoxButton.OK, MessageBoxImage.Information);
                // Refresh data if needed
                LoadData();
            }
        }

        private void CashCountButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Open cash count dialog
            MessageBox.Show("Comptage de caisse à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async void DatePicker_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
        {
            await LoadSessions();
        }

        private  void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
             LoadData();
        }

        private void ViewSessionButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is CashSession session)
            {
                // TODO: Open session details dialog
                MessageBox.Show($"Détails de la session {session.SessionNumber}", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void PrintSessionButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is CashSession session)
            {
                // TODO: Print session report
                MessageBox.Show($"Imprimer le rapport de la session {session.SessionNumber}", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void SessionsDataGrid_MouseDoubleClick(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (SessionsDataGrid.SelectedItem is CashSession session)
            {
                // TODO: Open session details dialog
                MessageBox.Show($"Détails de la session {session.SessionNumber}", "Information",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }
}
