<Page x:Class="GestioDeStock.Views.Pages.SuppliersPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="Gestion des Fournisseurs">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Style="{StaticResource CardStyle}">
            <DockPanel>
                <StackPanel DockPanel.Dock="Left">
                    <TextBlock Text="Gestion des Fournisseurs" FontSize="24" FontWeight="Bold"/>
                    <TextBlock Text="Gérer les informations fournisseurs et leurs comptes" FontSize="14"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>

                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="NewSupplierButton"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Content="NOUVEAU FOURNISSEUR"
                          Margin="10,0,0,0"
                          Click="NewSupplierButton_Click">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="TruckPlus" Width="16" Height="16"
                                                           VerticalAlignment="Center" Margin="0,0,5,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>
                </StackPanel>
            </DockPanel>
        </materialDesign:Card>

        <!-- Filters -->
        <materialDesign:Card Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBox x:Name="SearchTextBox" Grid.Column="0"
                       materialDesign:HintAssist.Hint="Rechercher par nom, téléphone, email..."
                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                       Margin="0,0,10,0"
                       TextChanged="SearchTextBox_TextChanged"/>

                <ComboBox x:Name="StatusComboBox" Grid.Column="1"
                        materialDesign:HintAssist.Hint="Statut"
                        Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                        Margin="0,0,10,0"
                        SelectionChanged="StatusComboBox_SelectionChanged">
                    <ComboBoxItem Content="Tous"/>
                    <ComboBoxItem Content="Actifs"/>
                    <ComboBoxItem Content="Inactifs"/>
                    <ComboBoxItem Content="Avec Dettes"/>
                </ComboBox>

                <Button x:Name="RefreshButton" Grid.Column="2"
                      Style="{StaticResource MaterialDesignIconButton}"
                      ToolTip="Actualiser"
                      Click="RefreshButton_Click">
                    <materialDesign:PackIcon Kind="Refresh"/>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Suppliers List -->
        <materialDesign:Card Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <DockPanel Grid.Row="0" Margin="0,0,0,10">
                    <TextBlock x:Name="ResultsCountTextBlock" Text="0 fournisseurs trouvés"
                             FontWeight="Medium" DockPanel.Dock="Left"/>

                    <StackPanel DockPanel.Dock="Right" Orientation="Horizontal">
                        <Button x:Name="ExportButton"
                              Style="{StaticResource MaterialDesignOutlinedButton}"
                              Content="EXPORTER" Margin="5,0"
                              Click="ExportButton_Click">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="FileExport" Width="16" Height="16"
                                                               VerticalAlignment="Center" Margin="0,0,5,0"/>
                                        <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>
                    </StackPanel>
                </DockPanel>

                <DataGrid x:Name="SuppliersDataGrid" Grid.Row="1"
                        AutoGenerateColumns="False"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        IsReadOnly="True"
                        SelectionMode="Single"
                        MouseDoubleClick="SuppliersDataGrid_MouseDoubleClick">

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Code" Binding="{Binding Code}" Width="100"/>
                        <DataGridTextColumn Header="Nom" Binding="{Binding Name}" Width="200"/>
                        <DataGridTextColumn Header="Téléphone" Binding="{Binding Phone}" Width="120"/>
                        <DataGridTextColumn Header="Email" Binding="{Binding Email}" Width="180"/>
                        <DataGridTextColumn Header="Adresse" Binding="{Binding Address}" Width="200"/>
                        <DataGridTextColumn Header="NIF" Binding="{Binding Nif}" Width="120"/>
                        <DataGridTextColumn Header="Solde Actuel" Binding="{Binding CurrentBalance, StringFormat='{}{0:N2} DA'}" Width="120"/>

                        <DataGridTemplateColumn Header="Statut" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="10" Padding="5,2" HorizontalAlignment="Center">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsActive}" Value="True">
                                                        <Setter Property="Background" Value="#4CAF50"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding IsActive}" Value="False">
                                                        <Setter Property="Background" Value="#F44336"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock Text="{Binding IsActive, Converter={StaticResource BoolToStatusConverter}}"
                                                 Foreground="White" FontSize="10" FontWeight="Bold"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="Actions" Width="150">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                              ToolTip="Modifier"
                                              Click="EditButton_Click"
                                              Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="Edit" Width="16" Height="16"/>
                                        </Button>

                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                              ToolTip="Compte Fournisseur"
                                              Click="AccountButton_Click"
                                              Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="AccountDetails" Width="16" Height="16"/>
                                        </Button>

                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                              ToolTip="Historique"
                                              Click="HistoryButton_Click"
                                              Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="History" Width="16" Height="16"/>
                                        </Button>

                                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                              ToolTip="Supprimer"
                                              Click="DeleteButton_Click"
                                              Tag="{Binding}">
                                            <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- Summary -->
                <materialDesign:Card Grid.Row="2" Margin="0,10,0,0" Padding="15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                            <TextBlock Text="Total Fournisseurs" FontSize="12" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="TotalSuppliersTextBlock" Text="0" FontSize="16" FontWeight="Bold"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                            <TextBlock Text="Fournisseurs Actifs" FontSize="12" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="ActiveSuppliersTextBlock" Text="0" FontSize="16" FontWeight="Bold" Foreground="#4CAF50"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                            <TextBlock Text="Total Dettes" FontSize="12" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="TotalDebtTextBlock" Text="0.00 DA" FontSize="16" FontWeight="Bold" Foreground="#F44336"/>
                        </StackPanel>

                        <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                            <TextBlock Text="Limite Crédit" FontSize="12" Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock x:Name="TotalCreditLimitTextBlock" Text="0.00 DA" FontSize="16" FontWeight="Bold"/>
                        </StackPanel>
                    </Grid>
                </materialDesign:Card>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Page>
