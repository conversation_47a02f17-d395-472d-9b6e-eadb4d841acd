using GestioDeStock.Data;
using GestioDeStock.Data.Repositories;
using GestioDeStock.Models;
using GestioDeStock.Services;
using System.Windows;
using System.Windows.Controls;

namespace GestioDeStock.Views.Dialogs
{
    public partial class AddClientDialog : Window
    {
        private readonly ClientRepository _clientRepository;
        private readonly AuthenticationService _authService;

        public Client? NewClient { get; private set; }

        public AddClientDialog(AuthenticationService authService)
        {
            InitializeComponent();
            _authService = authService;
            
            var config = ConfigurationService.Instance;
            var database = new DatabaseConnection(config.GetConnectionString());
            _clientRepository = new ClientRepository(database);
            
            LoadNextCode();
        }

        private async void LoadNextCode()
        {
            try
            {
                var nextCode = await _clientRepository.GetNextClientCodeAsync();
                CodeTextBox.Text = nextCode;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de la génération du code: {ex.Message}", 
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
                CodeTextBox.Text = "CL000001";
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validation
                if (string.IsNullOrWhiteSpace(NameTextBox.Text))
                {
                    MessageBox.Show("Le nom du client est obligatoire.", "Validation", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    NameTextBox.Focus();
                    return;
                }

                // Validate email format if provided
                if (!string.IsNullOrWhiteSpace(EmailTextBox.Text) && !IsValidEmail(EmailTextBox.Text))
                {
                    MessageBox.Show("Format d'email invalide.", "Validation", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    EmailTextBox.Focus();
                    return;
                }

                // Parse numeric values
                if (!decimal.TryParse(CreditLimitTextBox.Text, out decimal creditLimit) || creditLimit < 0)
                {
                    MessageBox.Show("La limite de crédit doit être un nombre positif.", "Validation", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    CreditLimitTextBox.Focus();
                    return;
                }

                if (!decimal.TryParse(OpeningBalanceTextBox.Text, out decimal openingBalance))
                {
                    MessageBox.Show("Le solde d'ouverture doit être un nombre valide.", "Validation", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    OpeningBalanceTextBox.Focus();
                    return;
                }

                // Create new client
                var newClient = new Client
                {
                    Code = CodeTextBox.Text.Trim(),
                    Name = NameTextBox.Text.Trim(),
                    Phone = PhoneTextBox.Text.Trim(),
                    Email = EmailTextBox.Text.Trim(),
                    Address = AddressTextBox.Text.Trim(),
                    Nif = NifTextBox.Text.Trim(),
                    Nis = NisTextBox.Text.Trim(),
                    Rc = RcTextBox.Text.Trim(),
                    Article = ArticleTextBox.Text.Trim(),
                    CreditLimit = creditLimit,
                    OpeningBalance = openingBalance,
                    CurrentBalance = openingBalance,
                    IsActive = IsActiveCheckBox.IsChecked ?? true,
                    IsArchived = false,
                    CreatedBy = _authService.CurrentUser?.Id ?? 0,
                    UpdatedBy = _authService.CurrentUser?.Id ?? 0
                };

                // Save to database
                var clientId = await _clientRepository.AddAsync(newClient);
                newClient.Id = clientId;

                NewClient = newClient;
                
                MessageBox.Show("Client ajouté avec succès!", "Succès", 
                              MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'enregistrement: {ex.Message}", 
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
}
