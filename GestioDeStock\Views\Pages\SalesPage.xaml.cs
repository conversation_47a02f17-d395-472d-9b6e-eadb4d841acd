using GestioDeStock.Data;
using GestioDeStock.Data.Repositories;
using GestioDeStock.Models;
using GestioDeStock.Services;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace GestioDeStock.Views.Pages
{
    public partial class SalesPage : Page
    {
        private readonly AuthenticationService _authService;
        private readonly ProductRepository _productRepository;
        private readonly ClientRepository _clientRepository;
        private readonly BarcodeService _barcodeService;
        private ObservableCollection<SaleItem> _cartItems;
        private SalesInvoice? _currentInvoice;

        public SalesPage(AuthenticationService authService)
        {
            InitializeComponent();
            _authService = authService;

            var config = ConfigurationService.Instance;
            var database = new DatabaseConnection(config.GetConnectionString());
            _productRepository = new ProductRepository(database);
            _clientRepository = new ClientRepository(database);
            _barcodeService = new BarcodeService();

            _cartItems = new ObservableCollection<SaleItem>();
            CartDataGrid.ItemsSource = _cartItems;

            // Subscribe to collection changes
            _cartItems.CollectionChanged += CartItems_CollectionChanged;

            InitializePage();
        }

        private async void InitializePage()
        {
            try
            {
                // Set cashier info
                CashierTextBlock.Text = $"Caissier: {_authService.CurrentUser?.FullName}";

                // Load clients
                var clients = await _clientRepository.GetActiveClientsAsync();
                ClientComboBox.ItemsSource = clients;
                ClientComboBox.DisplayMemberPath = "Name";
                ClientComboBox.SelectedValuePath = "Id";

                // Focus on barcode input
                BarcodeTextBox.Focus();

                // Initialize new sale
                StartNewSale();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'initialisation: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void StartNewSale()
        {
            _cartItems.Clear();
            _currentInvoice = new SalesInvoice
            {
                InvoiceDate = DateTime.Now,
                UserId = _authService.CurrentUser?.Id ?? 0,
                WarehouseId = 1, // TODO: Get from settings
                Status = InvoiceStatus.Brouillon,
                PaymentStatus = PaymentStatus.NonPayé
            };

            ClientComboBox.SelectedIndex = -1;
            DiscountTextBox.Text = "0";
            AmountReceivedTextBox.Clear();

            UpdateTotals();
        }

        private async void BarcodeTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                await ProcessBarcode();
            }
        }

        private async Task ProcessBarcode()
        {
            try
            {
                var barcode = BarcodeTextBox.Text.Trim();
                if (string.IsNullOrEmpty(barcode))
                    return;

                // Check if it's a scale barcode
                var scaleInfo = _barcodeService.ParseScaleBarcode(barcode);

                Product? product = null;
                decimal quantity = 1;

                if (scaleInfo.IsScaleBarcode)
                {
                    // Find product by scale code
                    product = await _productRepository.GetByCodeAsync(scaleInfo.ProductCode);
                    quantity = scaleInfo.IsWeightBased ? scaleInfo.Weight : 1;
                }
                else
                {
                    // Find product by regular barcode
                    product = await _productRepository.GetByBarcodeAsync(barcode);
                }

                if (product == null)
                {
                    MessageBox.Show("Produit non trouvé pour ce code-barres.", "Produit introuvable",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    BarcodeTextBox.SelectAll();
                    return;
                }

                // Add to cart
                AddToCart(product, quantity);

                // Clear barcode input
                BarcodeTextBox.Clear();
                BarcodeTextBox.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du traitement du code-barres: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddToCart(Product product, decimal quantity)
        {
            // Check if product already in cart
            var existingItem = _cartItems.FirstOrDefault(i => i.ProductId == product.Id);

            if (existingItem != null)
            {
                existingItem.Quantity += quantity;
            }
            else
            {
                var saleItem = new SaleItem
                {
                    ProductId = product.Id,
                    ProductName = product.Name,
                    ProductCode = product.Code,
                    UnitPrice = product.PrixVenteTtc,
                    Quantity = quantity,
                    TvaRate = product.TvaRate
                };

                _cartItems.Add(saleItem);
            }

            UpdateTotals();
        }

        private void CartItems_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            UpdateTotals();
        }

        private void UpdateTotals()
        {
            if (_currentInvoice == null) return;

            decimal subtotalHT = 0;
            decimal totalTVA = 0;

            foreach (var item in _cartItems)
            {
                var lineHT = item.Quantity * (item.UnitPrice / (1 + item.TvaRate / 100));
                var lineTVA = item.Quantity * item.UnitPrice - lineHT;

                subtotalHT += lineHT;
                totalTVA += lineTVA;
            }

            var subtotalTTC = subtotalHT + totalTVA;

            // Apply discount
            decimal discountAmount = 0;
            if (decimal.TryParse(DiscountTextBox.Text, out decimal discount))
            {
                if (DiscountTypeComboBox.SelectedIndex == 0) // Percentage
                {
                    discountAmount = subtotalTTC * (discount / 100);
                }
                else // Fixed amount
                {
                    discountAmount = discount;
                }
            }

            var finalAmount = subtotalTTC - discountAmount;

            // Calculate timbre
            var paymentMethod = PaymentMethodComboBox.SelectedIndex == 0 ? PaymentMethod.Espèce : PaymentMethod.Carte;
            var timbreAmount = _barcodeService.CalculateTimbre(finalAmount, paymentMethod);

            finalAmount += timbreAmount;

            // Update UI
            SubtotalHTTextBlock.Text = $"{subtotalHT:N2} DA";
            TVATextBlock.Text = $"{totalTVA:N2} DA";
            SubtotalTTCTextBlock.Text = $"{subtotalTTC:N2} DA";
            TimbreTextBlock.Text = $"{timbreAmount:N2} DA";
            FinalTotalTextBlock.Text = $"{finalAmount:N2} DA";

            // Update invoice
            _currentInvoice.SubtotalHt = subtotalHT;
            _currentInvoice.TotalTva = totalTVA;
            _currentInvoice.TotalTtc = subtotalTTC;
            _currentInvoice.DiscountAmount = discountAmount;
            _currentInvoice.TimbreAmount = timbreAmount;
            _currentInvoice.FinalAmount = finalAmount;

            // Update change
            UpdateChange();
        }

        private void UpdateChange()
        {
            if (_currentInvoice == null) return;

            if (decimal.TryParse(AmountReceivedTextBox.Text, out decimal amountReceived))
            {
                var change = amountReceived - _currentInvoice.FinalAmount;
                ChangeTextBlock.Text = $"{Math.Max(0, change):N2} DA";
            }
            else
            {
                ChangeTextBlock.Text = "0.00 DA";
            }
        }

        private void DiscountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateTotals();
        }

        private void PaymentMethodComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateTotals();
        }

        private void AmountReceivedTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdateChange();
        }

        private void RemoveItemButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is SaleItem item)
            {
                _cartItems.Remove(item);
            }
        }

        private void NewSaleButton_Click(object sender, RoutedEventArgs e)
        {
            if (_cartItems.Count > 0)
            {
                var result = MessageBox.Show("Voulez-vous vraiment commencer une nouvelle vente ? Les articles actuels seront perdus.",
                                           "Nouvelle Vente", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result != MessageBoxResult.Yes)
                    return;
            }

            StartNewSale();
        }

        private void HoldSaleButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement hold sale functionality
            MessageBox.Show("Fonctionnalité de suspension à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void SearchProductButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new Views.Dialogs.ProductSearchDialog();
            if (dialog.ShowDialog() == true && dialog.SelectedProduct != null)
            {
                AddProductToCart(dialog.SelectedProduct, 1);
            }
        }

        private void ManualEntryButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new Views.Dialogs.ManualEntryDialog();
            if (dialog.ShowDialog() == true && dialog.SelectedProduct != null)
            {
                AddProductToCart(dialog.SelectedProduct, dialog.Quantity, dialog.UnitPrice, dialog.DiscountPercentage);
            }
        }

        private void ClearCartButton_Click(object sender, RoutedEventArgs e)
        {
            if (_cartItems.Count == 0) return;

            var result = MessageBox.Show("Voulez-vous vraiment vider le panier ?",
                                       "Vider le Panier", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                _cartItems.Clear();
            }
        }

        private void AddClientButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new Views.Dialogs.AddClientDialog(_authService);
            if (dialog.ShowDialog() == true && dialog.NewClient != null)
            {
                // Refresh client list and select the new client
                LoadClients();
                ClientComboBox.SelectedValue = dialog.NewClient.Id;
                MessageBox.Show($"Client '{dialog.NewClient.Name}' ajouté avec succès!", "Succès",
                              MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void ProcessSaleButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_cartItems.Count == 0)
                {
                    MessageBox.Show("Le panier est vide.", "Validation",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (_currentInvoice == null) return;

                // Validate payment
                if (!decimal.TryParse(AmountReceivedTextBox.Text, out decimal amountReceived) ||
                    amountReceived < _currentInvoice.FinalAmount)
                {
                    MessageBox.Show("Le montant reçu est insuffisant.", "Validation",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // TODO: Save invoice to database
                // TODO: Update stock
                // TODO: Print receipt

                MessageBox.Show("Vente enregistrée avec succès!", "Succès",
                              MessageBoxButton.OK, MessageBoxImage.Information);

                StartNewSale();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de la validation: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement printing
            MessageBox.Show("Impression à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void EmailButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement email
            MessageBox.Show("Envoi par email à implémenter", "Information",
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AddProductToCart(Product product, decimal quantity = 1, decimal? customPrice = null, decimal discountPercentage = 0)
        {
            try
            {
                // Check if product already exists in cart
                var existingItem = _cartItems.FirstOrDefault(item => item.ProductId == product.Id);

                if (existingItem != null)
                {
                    // Update quantity
                    existingItem.Quantity += quantity;
                }
                else
                {
                    // Add new item
                    var unitPrice = customPrice ?? product.PrixVenteTtc;

                    // Apply discount if any
                    if (discountPercentage > 0)
                    {
                        unitPrice = unitPrice * (1 - discountPercentage / 100);
                    }

                    var newItem = new SaleItem
                    {
                        ProductId = product.Id,
                        ProductName = product.Name,
                        ProductCode = product.Code,
                        UnitPrice = unitPrice,
                        TvaRate = product.TvaRate,
                        Quantity = quantity
                    };

                    _cartItems.Add(newItem);
                }

                UpdateTotals();
                BarcodeTextBox.Clear();
                BarcodeTextBox.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'ajout du produit: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    // Helper class for cart items
    public class SaleItem : INotifyPropertyChanged
    {
        private decimal _quantity;

        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string ProductCode { get; set; } = string.Empty;
        public decimal UnitPrice { get; set; }
        public decimal TvaRate { get; set; }

        public decimal Quantity
        {
            get => _quantity;
            set
            {
                _quantity = value;
                OnPropertyChanged(nameof(Quantity));
                OnPropertyChanged(nameof(LineTotal));
            }
        }

        public decimal LineTotal => Quantity * UnitPrice;

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
