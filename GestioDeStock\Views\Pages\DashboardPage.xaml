<Page x:Class="GestioDeStock.Views.Pages.DashboardPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="Tableau de Bord">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Welcome Header -->
            <materialDesign:Card Grid.Row="0" Style="{StaticResource CardStyle}">
                <StackPanel>
                    <TextBlock Text="Tableau de Bord" FontSize="24" FontWeight="Bold" Margin="0,0,0,10"/>
                    <TextBlock x:Name="WelcomeTextBlock" FontSize="16" 
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>
            </materialDesign:Card>
            
            <!-- Statistics Cards -->
            <Grid Grid.Row="1" Margin="0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- Today's Sales -->
                <materialDesign:Card Grid.Column="0" Style="{StaticResource CardStyle}" 
                                   Background="{DynamicResource PrimaryHueMidBrush}">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="CashRegister" Width="32" Height="32" 
                                                   Foreground="White" DockPanel.Dock="Left"/>
                            <StackPanel DockPanel.Dock="Right" HorizontalAlignment="Right">
                                <TextBlock x:Name="TodaySalesAmountTextBlock" Text="0 DA" 
                                         FontSize="20" FontWeight="Bold" Foreground="White" 
                                         HorizontalAlignment="Right"/>
                                <TextBlock Text="Ventes du jour" FontSize="12" 
                                         Foreground="White" HorizontalAlignment="Right"/>
                            </StackPanel>
                        </DockPanel>
                    </StackPanel>
                </materialDesign:Card>
                
                <!-- Total Products -->
                <materialDesign:Card Grid.Column="1" Style="{StaticResource CardStyle}" 
                                   Background="{DynamicResource SecondaryHueMidBrush}">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="Package" Width="32" Height="32" 
                                                   Foreground="White" DockPanel.Dock="Left"/>
                            <StackPanel DockPanel.Dock="Right" HorizontalAlignment="Right">
                                <TextBlock x:Name="TotalProductsTextBlock" Text="0" 
                                         FontSize="20" FontWeight="Bold" Foreground="White" 
                                         HorizontalAlignment="Right"/>
                                <TextBlock Text="Produits" FontSize="12" 
                                         Foreground="White" HorizontalAlignment="Right"/>
                            </StackPanel>
                        </DockPanel>
                    </StackPanel>
                </materialDesign:Card>
                
                <!-- Low Stock -->
                <materialDesign:Card Grid.Column="2" Style="{StaticResource CardStyle}" 
                                   Background="#FF9800">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="AlertCircle" Width="32" Height="32" 
                                                   Foreground="White" DockPanel.Dock="Left"/>
                            <StackPanel DockPanel.Dock="Right" HorizontalAlignment="Right">
                                <TextBlock x:Name="LowStockTextBlock" Text="0" 
                                         FontSize="20" FontWeight="Bold" Foreground="White" 
                                         HorizontalAlignment="Right"/>
                                <TextBlock Text="Stock faible" FontSize="12" 
                                         Foreground="White" HorizontalAlignment="Right"/>
                            </StackPanel>
                        </DockPanel>
                    </StackPanel>
                </materialDesign:Card>
                
                <!-- Total Clients -->
                <materialDesign:Card Grid.Column="3" Style="{StaticResource CardStyle}" 
                                   Background="#4CAF50">
                    <StackPanel>
                        <DockPanel>
                            <materialDesign:PackIcon Kind="AccountGroup" Width="32" Height="32" 
                                                   Foreground="White" DockPanel.Dock="Left"/>
                            <StackPanel DockPanel.Dock="Right" HorizontalAlignment="Right">
                                <TextBlock x:Name="TotalClientsTextBlock" Text="0" 
                                         FontSize="20" FontWeight="Bold" Foreground="White" 
                                         HorizontalAlignment="Right"/>
                                <TextBlock Text="Clients" FontSize="12" 
                                         Foreground="White" HorizontalAlignment="Right"/>
                            </StackPanel>
                        </DockPanel>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>
            
            <!-- Quick Actions -->
            <materialDesign:Card Grid.Row="2" Style="{StaticResource CardStyle}">
                <StackPanel>
                    <TextBlock Text="Actions Rapides" FontSize="18" FontWeight="Medium" Margin="0,0,0,15"/>
                    <UniformGrid Columns="4" Rows="2">
                        
                        <Button x:Name="NewSaleButton" Style="{StaticResource MaterialDesignRaisedButton}" 
                              Margin="5" Height="60" Click="NewSaleButton_Click">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="CashRegister" Width="24" Height="24"/>
                                <TextBlock Text="Nouvelle Vente" FontSize="12" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>
                        
                        <Button x:Name="AddProductButton" Style="{StaticResource MaterialDesignRaisedButton}" 
                              Margin="5" Height="60" Click="AddProductButton_Click">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="PackageVariantClosed" Width="24" Height="24"/>
                                <TextBlock Text="Ajouter Produit" FontSize="12" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>
                        
                        <Button x:Name="NewPurchaseButton" Style="{StaticResource MaterialDesignRaisedButton}" 
                              Margin="5" Height="60" Click="NewPurchaseButton_Click">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="ShoppingCart" Width="24" Height="24"/>
                                <TextBlock Text="Nouvel Achat" FontSize="12" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>
                        
                        <Button x:Name="StockReportButton" Style="{StaticResource MaterialDesignRaisedButton}" 
                              Margin="5" Height="60" Click="StockReportButton_Click">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="ChartLine" Width="24" Height="24"/>
                                <TextBlock Text="Rapport Stock" FontSize="12" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>
                        
                        <Button x:Name="AddClientButton" Style="{StaticResource MaterialDesignRaisedButton}" 
                              Margin="5" Height="60" Click="AddClientButton_Click">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="AccountPlus" Width="24" Height="24"/>
                                <TextBlock Text="Ajouter Client" FontSize="12" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>
                        
                        <Button x:Name="CashSessionButton" Style="{StaticResource MaterialDesignRaisedButton}" 
                              Margin="5" Height="60" Click="CashSessionButton_Click">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="Cash" Width="24" Height="24"/>
                                <TextBlock Text="Session Caisse" FontSize="12" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>
                        
                        <Button x:Name="BackupButton" Style="{StaticResource MaterialDesignRaisedButton}" 
                              Margin="5" Height="60" Click="BackupButton_Click">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="DatabaseExport" Width="24" Height="24"/>
                                <TextBlock Text="Sauvegarde" FontSize="12" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>
                        
                        <Button x:Name="SettingsButton" Style="{StaticResource MaterialDesignRaisedButton}" 
                              Margin="5" Height="60" Click="SettingsButton_Click">
                            <StackPanel>
                                <materialDesign:PackIcon Kind="Settings" Width="24" Height="24"/>
                                <TextBlock Text="Paramètres" FontSize="12" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>
                        
                    </UniformGrid>
                </StackPanel>
            </materialDesign:Card>
            
            <!-- Recent Activities -->
            <Grid Grid.Row="3" Margin="0,10,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- Recent Sales -->
                <materialDesign:Card Grid.Column="0" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="Ventes Récentes" FontSize="16" FontWeight="Medium" Margin="0,0,0,10"/>
                        <DataGrid x:Name="RecentSalesDataGrid" 
                                AutoGenerateColumns="False" 
                                CanUserAddRows="False"
                                CanUserDeleteRows="False"
                                IsReadOnly="True"
                                Height="200">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="N° Facture" Binding="{Binding InvoiceNumber}" Width="100"/>
                                <DataGridTextColumn Header="Client" Binding="{Binding ClientName}" Width="120"/>
                                <DataGridTextColumn Header="Montant" Binding="{Binding FinalAmount, StringFormat='{}{0:N2} DA'}" Width="100"/>
                                <DataGridTextColumn Header="Date" Binding="{Binding InvoiceDate, StringFormat='{}{0:dd/MM/yyyy HH:mm}'}" Width="120"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </materialDesign:Card>
                
                <!-- Low Stock Products -->
                <materialDesign:Card Grid.Column="1" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="Produits en Stock Faible" FontSize="16" FontWeight="Medium" Margin="0,0,0,10"/>
                        <DataGrid x:Name="LowStockDataGrid" 
                                AutoGenerateColumns="False" 
                                CanUserAddRows="False"
                                CanUserDeleteRows="False"
                                IsReadOnly="True"
                                Height="200">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="Code" Binding="{Binding Code}" Width="80"/>
                                <DataGridTextColumn Header="Nom" Binding="{Binding Name}" Width="150"/>
                                <DataGridTextColumn Header="Stock" Binding="{Binding CurrentStock, StringFormat='{}{0:N2}'}" Width="80"/>
                                <DataGridTextColumn Header="Alerte" Binding="{Binding MinStockAlert, StringFormat='{}{0:N2}'}" Width="80"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </materialDesign:Card>
                
            </Grid>
            
        </Grid>
    </ScrollViewer>
</Page>
