<Page x:Class="GestioDeStock.Views.Pages.SettingsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
      Title="Paramètres du Système">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Style="{StaticResource CardStyle}">
            <DockPanel>
                <StackPanel DockPanel.Dock="Left">
                    <TextBlock Text="Paramètres du Système" FontSize="24" FontWeight="Bold"/>
                    <TextBlock Text="Configuration générale de l'application" FontSize="14"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>

                <StackPanel DockPanel.Dock="Right" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="SaveButton"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Content="ENREGISTRER"
                          Margin="10,0,0,0"
                          Click="SaveButton_Click">
                        <Button.ContentTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ContentSave" Width="16" Height="16"
                                                           VerticalAlignment="Center" Margin="0,0,5,0"/>
                                    <TextBlock Text="{Binding}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </DataTemplate>
                        </Button.ContentTemplate>
                    </Button>
                </StackPanel>
            </DockPanel>
        </materialDesign:Card>

        <!-- Settings Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>

                <!-- Company Information -->
                <materialDesign:Card Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="Informations de l'Entreprise" FontSize="18" FontWeight="Medium" Margin="0,0,0,15"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBox x:Name="CompanyNameTextBox" Grid.Row="0" Grid.Column="0"
                                   materialDesign:HintAssist.Hint="Nom de l'entreprise"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Margin="0,0,10,10"/>

                            <TextBox x:Name="CompanyPhoneTextBox" Grid.Row="0" Grid.Column="1"
                                   materialDesign:HintAssist.Hint="Téléphone"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Margin="10,0,0,10"/>

                            <TextBox x:Name="CompanyEmailTextBox" Grid.Row="1" Grid.Column="0"
                                   materialDesign:HintAssist.Hint="Email"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Margin="0,0,10,10"/>

                            <TextBox x:Name="CompanyWebsiteTextBox" Grid.Row="1" Grid.Column="1"
                                   materialDesign:HintAssist.Hint="Site web"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Margin="10,0,0,10"/>

                            <TextBox x:Name="CompanyAddressTextBox" Grid.Row="2" Grid.ColumnSpan="2"
                                   materialDesign:HintAssist.Hint="Adresse complète"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Margin="0,0,0,10"/>

                            <TextBox x:Name="CompanyNifTextBox" Grid.Row="3" Grid.Column="0"
                                   materialDesign:HintAssist.Hint="NIF"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Margin="0,0,10,10"/>

                            <TextBox x:Name="CompanyNisTextBox" Grid.Row="3" Grid.Column="1"
                                   materialDesign:HintAssist.Hint="NIS"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Margin="10,0,0,10"/>

                            <TextBox x:Name="CompanyRcTextBox" Grid.Row="4" Grid.Column="0"
                                   materialDesign:HintAssist.Hint="RC"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Margin="0,0,10,0"/>

                            <TextBox x:Name="CompanyArticleTextBox" Grid.Row="4" Grid.Column="1"
                                   materialDesign:HintAssist.Hint="Article"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Margin="10,0,0,0"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Application Settings -->
                <materialDesign:Card Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="Paramètres de l'Application" FontSize="18" FontWeight="Medium" Margin="0,0,0,15"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <ComboBox x:Name="DefaultWarehouseComboBox" Grid.Row="0" Grid.Column="0"
                                    materialDesign:HintAssist.Hint="Magasin par défaut"
                                    Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                                    Margin="0,0,10,10"/>

                            <ComboBox x:Name="CurrencyComboBox" Grid.Row="0" Grid.Column="1"
                                    materialDesign:HintAssist.Hint="Devise"
                                    Style="{StaticResource MaterialDesignFloatingHintComboBox}"
                                    Margin="10,0,0,10">
                                <ComboBoxItem Content="DA - Dinar Algérien" IsSelected="True"/>
                                <ComboBoxItem Content="EUR - Euro"/>
                                <ComboBoxItem Content="USD - Dollar US"/>
                            </ComboBox>

                            <TextBox x:Name="BackupPathTextBox" Grid.Row="1" Grid.ColumnSpan="2"
                                   materialDesign:HintAssist.Hint="Chemin des sauvegardes"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Margin="0,0,0,10"/>

                            <CheckBox x:Name="AutoBackupCheckBox" Grid.Row="2" Grid.Column="0"
                                    Content="Sauvegarde automatique"
                                    Style="{StaticResource MaterialDesignCheckBox}"
                                    Margin="0,10"/>

                            <TextBox x:Name="BackupIntervalTextBox" Grid.Row="2" Grid.Column="1"
                                   materialDesign:HintAssist.Hint="Intervalle (heures)"
                                   Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                                   Margin="10,0,0,10"/>

                            <CheckBox x:Name="PrintLogoCheckBox" Grid.Row="3" Grid.Column="0"
                                    Content="Imprimer le logo"
                                    Style="{StaticResource MaterialDesignCheckBox}"
                                    Margin="0,10"/>

                            <CheckBox x:Name="PrintFooterCheckBox" Grid.Row="3" Grid.Column="1"
                                    Content="Imprimer le pied de page"
                                    Style="{StaticResource MaterialDesignCheckBox}"
                                    Margin="10,10,0,10"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

            </StackPanel>
        </ScrollViewer>
    </Grid>
</Page>
