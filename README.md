# نظام إدارة المخزون للبقالة - Gestion de Stock

نظام إدارة متكامل لمحلات البقالة والمواد الغذائية باستخدام C# .NET Core 6.0 مع MaterialDesign WPF و Dapper و MySQL.

## المميزات الرئيسية

### 🧱 إدارة المنتجات (Produits)
- إدارة كاملة للمنتجات مع الصور والماركات
- دعم تواريخ انتهاء الصلاحية
- أسعار الشراء والبيع (HT/TTC)
- حساب PMP، LIFO، FIFO
- إدارة الفاردو والوحدات المتعددة
- تنبيهات المخزون المنخفض

### 🔁 دعم الباركود المتقدم
- دعم الباركودات المتعددة لكل منتج
- باركود الميزان (يبدأ بـ 22 أو 02)
- استخلاص الوزن أو السعر تلقائياً من الباركود
- دعم وحدة الكيلوغرام والسعر الجزئي

### 🛒 إدارة المبيعات (Ventes)
- نقطة بيع متقدمة مع دعم الباركود
- فواتير بيع شاملة (TTC, HT, TVA, خصم)
- الطابع الجبائي حسب القوانين الجزائرية
- طرق دفع متعددة (نقداً، بنكي، شيك، آجل)
- مردودات المبيعات
- دعم الطابعة الحرارية

### 📥 إدارة المشتريات (Achats)
- تسجيل فواتير الشراء
- ربط بالموردين
- تحديث تلقائي لـ PMP
- مردودات الشراء
- دعم الطابع الجبائي

### 📦 إدارة المخزون (Stock)
- مخازن متعددة
- تحويلات بين المخازن
- حركات المخزون (دخول/خروج)
- تتبع FIFO, LIFO, PMP
- جرد المخزون

### 🧾 العملاء والموردين
- إدارة كاملة للعملاء والموردين
- معلومات الشركة (NIF، NIS، RC، Article)
- كشوف الحسابات
- أرصدة افتتاحية

### 💸 المصاريف والإيرادات
- تصنيف المصاريف والإيرادات
- ربط بالحسابات البنكية
- تقارير مفصلة

### 🏦 الخزينة والبنوك
- إدارة جلسات الكاشير
- حسابات بنكية متعددة
- تحويلات بين البنك والخزينة

### 📊 التقارير ولوحة التحكم
- إحصائيات المبيعات
- تقارير المخزون
- رسوم بيانية
- تصدير PDF/Excel

## المتطلبات التقنية

- .NET 6.0 Windows
- MySQL 8.0+
- Windows 10/11

## الحزم المستخدمة

- **MaterialDesignThemes** - واجهة المستخدم الحديثة
- **Dapper** - ORM خفيف الوزن
- **MySql.Data** - اتصال قاعدة البيانات
- **BCrypt.Net** - تشفير كلمات المرور
- **iTextSharp** - إنشاء ملفات PDF
- **ZXing.Net** - قراءة الباركود

## التثبيت والإعداد

### 1. إعداد قاعدة البيانات
```sql
-- تشغيل ملف database.sql في MySQL
mysql -u root -p < database.sql
```

### 2. تكوين الاتصال
عدّل ملف `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=gestion_stock;Uid=root;Pwd=your_password;CharSet=utf8mb4;"
  }
}
```

### 3. التشغيل السريع
```bash
# تشغيل مباشر
start.bat

# أو يدوياً
cd GestioDeStock
dotnet restore
dotnet build
dotnet run
```

### 4. ملفات التشغيل المتوفرة
- `start.bat` - قائمة تشغيل تفاعلية
- `run.bat` - تشغيل سريع
- `dev.bat` - وضع التطوير
- `build-release.bat` - بناء نسخة الإنتاج

## بيانات الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** admin

## هيكل المشروع

```
GestioDeStock/
├── Models/              # نماذج البيانات
├── Data/               # طبقة الوصول للبيانات
├── Services/           # الخدمات
├── Views/              # واجهات المستخدم
│   ├── Pages/          # صفحات التطبيق
│   └── LoginWindow.xaml # شاشة تسجيل الدخول
├── App.xaml            # إعدادات التطبيق
└── MainWindow.xaml     # النافذة الرئيسية
```

## الميزات المتقدمة

### حساب الطابع الجبائي
```csharp
// حساب تلقائي حسب القوانين الجزائرية
≤ 300 DA → 0 DA
300.01 DA إلى 30,000 DA → 1 DA
30,000.01 DA إلى 100,000 DA → 1.5 DA لكل 100 DA
```

### باركود الميزان
```
هيكل الباركود: PPCCCCQQQQQC
PP = Préfixe (22 أو 02)
CCCC = Code Produit
QQQQQ = Poids/Prix
C = Check digit
```

## الأمان

- تشفير كلمات المرور باستخدام BCrypt
- صلاحيات المستخدمين حسب الأدوار
- تسجيل عمليات الدخول

## الدعم والتطوير

هذا النظام قابل للتوسع ويمكن إضافة المزيد من الميزات:
- تكامل مع أنظمة المحاسبة
- تطبيق موبايل
- تقارير متقدمة
- نسخ احتياطية تلقائية

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التجاري والشخصي.

---

**تم تطويره باستخدام أحدث التقنيات لضمان الأداء والموثوقية**
