﻿<Application x:Class="GestioDeStock.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:GestioDeStock"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="Views/LoginWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Orange" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Styles personnalisés -->
            <Style x:Key="MenuButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
                <Setter Property="Height" Value="50"/>
                <Setter Property="Margin" Value="5"/>
                <Setter Property="HorizontalAlignment" Value="Stretch"/>
                <Setter Property="HorizontalContentAlignment" Value="Left"/>
                <Setter Property="Padding" Value="15,0"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Foreground" Value="White"/>
            </Style>

            <Style x:Key="CardStyle" TargetType="materialDesign:Card">
                <Setter Property="Margin" Value="10"/>
                <Setter Property="Padding" Value="15"/>
                <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
            </Style>

        </ResourceDictionary>
    </Application.Resources>
</Application>
