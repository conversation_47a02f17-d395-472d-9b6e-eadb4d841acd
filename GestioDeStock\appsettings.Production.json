{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=gestion_stock;Uid=gestion_user;Pwd=your_secure_password;CharSet=utf8mb4;Connection Timeout=30;"}, "AppSettings": {"AutoBackupEnabled": true, "AutoBackupIntervalHours": 6}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "EnableFileLogging": true, "LogFilePath": "Logs\\production.log", "MaxLogFileSizeMB": 50, "MaxLogFiles": 10}, "SecuritySettings": {"PasswordMinLength": 8, "RequireUppercase": true, "RequireNumbers": true, "RequireSpecialChars": true, "PasswordExpiryDays": 90, "EnableAuditLog": true}, "PrinterSettings": {"ThermalPrinterEnabled": true, "PrintLogo": true, "PrintFooter": true}}