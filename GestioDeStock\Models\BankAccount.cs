using System.Collections.Generic;

namespace GestioDeStock.Models
{
    public class BankAccount : BaseEntity
    {
        public string AccountName { get; set; } = string.Empty;
        public string BankName { get; set; } = string.Empty;
        public string AccountNumber { get; set; } = string.Empty;
        public string? Rib { get; set; }
        public string? SwiftCode { get; set; }
        public decimal OpeningBalance { get; set; } = 0.00m;
        public decimal CurrentBalance { get; set; } = 0.00m;
        public bool IsActive { get; set; } = true;

        // Navigation properties
        public List<SalesPayment> SalesPayments { get; set; } = new List<SalesPayment>();
        public List<Expense> Expenses { get; set; } = new List<Expense>();
    }
}
