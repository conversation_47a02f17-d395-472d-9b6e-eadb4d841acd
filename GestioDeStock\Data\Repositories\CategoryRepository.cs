using Dapper;
using GestioDeStock.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace GestioDeStock.Data.Repositories
{
    public class CategoryRepository : BaseRepository<Category>
    {
        public CategoryRepository(DatabaseConnection database) : base(database, "categories")
        {
        }

        public override async Task<int> AddAsync(Category entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                INSERT INTO categories (name, description, parent_id, is_active, created_by, updated_by)
                VALUES (@Name, @Description, @ParentId, @IsActive, @CreatedBy, @UpdatedBy);
                SELECT LAST_INSERT_ID();";
            
            return await connection.QuerySingleAsync<int>(sql, entity);
        }

        public override async Task<bool> UpdateAsync(Category entity)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                UPDATE categories SET 
                    name = @Name,
                    description = @Description,
                    parent_id = @ParentId,
                    is_active = @IsActive,
                    updated_at = NOW(),
                    updated_by = @UpdatedBy
                WHERE id = @Id";
            
            var affectedRows = await connection.ExecuteAsync(sql, entity);
            return affectedRows > 0;
        }

        public override async Task<IEnumerable<Category>> SearchAsync(string searchTerm)
        {
            using var connection = _database.CreateConnection();
            var sql = @"
                SELECT * FROM categories 
                WHERE name LIKE @SearchTerm 
                   OR description LIKE @SearchTerm
                ORDER BY name";
            
            return await connection.QueryAsync<Category>(sql, new { SearchTerm = $"%{searchTerm}%" });
        }

        public async Task<IEnumerable<Category>> GetMainCategoriesAsync()
        {
            using var connection = _database.CreateConnection();
            var sql = "SELECT * FROM categories WHERE parent_id IS NULL AND is_active = 1 ORDER BY name";
            return await connection.QueryAsync<Category>(sql);
        }

        public async Task<IEnumerable<Category>> GetSubCategoriesAsync(int parentId)
        {
            using var connection = _database.CreateConnection();
            var sql = "SELECT * FROM categories WHERE parent_id = @ParentId AND is_active = 1 ORDER BY name";
            return await connection.QueryAsync<Category>(sql, new { ParentId = parentId });
        }

        public async Task<IEnumerable<Category>> GetActiveCategoriesAsync()
        {
            return await GetActiveAsync();
        }
    }
}
