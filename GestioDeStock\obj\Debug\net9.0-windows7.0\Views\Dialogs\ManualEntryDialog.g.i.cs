﻿#pragma checksum "..\..\..\..\..\Views\Dialogs\ManualEntryDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "5637E9E138C7007D40E9FBB01D2A4D653DCF86BE"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GestioDeStock.Views.Dialogs {
    
    
    /// <summary>
    /// ManualEntryDialog
    /// </summary>
    public partial class ManualEntryDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 22 "..\..\..\..\..\Views\Dialogs\ManualEntryDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ProductComboBox;
        
        #line default
        #line hidden
        
        
        #line 33 "..\..\..\..\..\Views\Dialogs\ManualEntryDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox QuantityTextBox;
        
        #line default
        #line hidden
        
        
        #line 40 "..\..\..\..\..\Views\Dialogs\ManualEntryDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UnitPriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\..\Views\Dialogs\ManualEntryDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DiscountTextBox;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\..\Views\Dialogs\ManualEntryDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SubtotalTextBlock;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\..\Views\Dialogs\ManualEntryDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DiscountAmountTextBlock;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\..\Views\Dialogs\ManualEntryDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TvaTextBlock;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\..\Views\Dialogs\ManualEntryDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalTextBlock;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\..\Views\Dialogs\ManualEntryDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddButton;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\..\Views\Dialogs\ManualEntryDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GestioDeStock;V1.0.0.0;component/views/dialogs/manualentrydialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Dialogs\ManualEntryDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ProductComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 25 "..\..\..\..\..\Views\Dialogs\ManualEntryDialog.xaml"
            this.ProductComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ProductComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.QuantityTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 38 "..\..\..\..\..\Views\Dialogs\ManualEntryDialog.xaml"
            this.QuantityTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.QuantityTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.UnitPriceTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 44 "..\..\..\..\..\Views\Dialogs\ManualEntryDialog.xaml"
            this.UnitPriceTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.UnitPriceTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.DiscountTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 51 "..\..\..\..\..\Views\Dialogs\ManualEntryDialog.xaml"
            this.DiscountTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.DiscountTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.SubtotalTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.DiscountAmountTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TvaTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TotalTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.AddButton = ((System.Windows.Controls.Button)(target));
            
            #line 90 "..\..\..\..\..\Views\Dialogs\ManualEntryDialog.xaml"
            this.AddButton.Click += new System.Windows.RoutedEventHandler(this.AddButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 96 "..\..\..\..\..\Views\Dialogs\ManualEntryDialog.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

