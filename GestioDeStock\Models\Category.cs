using System.Collections.Generic;

namespace GestioDeStock.Models
{
    public class Category : BaseEntity
    {
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public int? ParentId { get; set; }
        public bool IsActive { get; set; } = true;

        // Navigation properties
        public Category? Parent { get; set; }
        public List<Category> Children { get; set; } = new List<Category>();
        public List<Product> Products { get; set; } = new List<Product>();
    }
}
